.is-small-text{
  font-size:.875em;
}

.is-regular-text{
  font-size:1em;
}

.is-large-text{
  font-size:2.25em;
}

.is-larger-text{
  font-size:3em;
}

.has-drop-cap:not(:focus):first-letter{
  float:left;
  font-size:8.4em;
  font-style:normal;
  font-weight:100;
  line-height:.68;
  margin:.05em .1em 0 0;
  text-transform:uppercase;
}

body.rtl .has-drop-cap:not(:focus):first-letter{
  float:none;
  margin-left:.1em;
}

p.has-drop-cap.has-background{
  overflow:hidden;
}

:root :where(p.has-background){
  padding:1.25em 2.375em;
}

:where(p.has-text-color:not(.has-link-color)) a{
  color:inherit;
}

p.has-text-align-left[style*="writing-mode:vertical-lr"],p.has-text-align-right[style*="writing-mode:vertical-rl"]{
  rotate:180deg;
}