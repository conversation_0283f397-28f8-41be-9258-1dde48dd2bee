(()=>{function e(e){return void 0===e?0:Number(e)}function t(e,t){return!(e===t||isNaN(e)&&isNaN(t))}self.DOMRect=function(n,i,u,r){var o,f,a,c,m=e(n),b=e(i),d=e(u),g=e(r);Object.defineProperties(this,{x:{get:function(){return m},set:function(e){t(m,e)&&(m=e,o=f=void 0)},enumerable:!0},y:{get:function(){return b},set:function(e){t(b,e)&&(b=e,a=c=void 0)},enumerable:!0},width:{get:function(){return d},set:function(e){t(d,e)&&(d=e,o=f=void 0)},enumerable:!0},height:{get:function(){return g},set:function(e){t(g,e)&&(g=e,a=c=void 0)},enumerable:!0},left:{get:function(){return o=void 0===o?m+Math.min(0,d):o},enumerable:!0},right:{get:function(){return f=void 0===f?m+Math.max(0,d):f},enumerable:!0},top:{get:function(){return a=void 0===a?b+Math.min(0,g):a},enumerable:!0},bottom:{get:function(){return c=void 0===c?b+Math.max(0,g):c},enumerable:!0}})}})();