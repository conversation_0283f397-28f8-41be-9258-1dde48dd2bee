/*!
 * jQuery UI Effects Scale 1.13.3
 * https://jqueryui.com
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license.
 * https://jquery.org/license
 */
!function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery","../version","../effect","./effect-size"],e):e(jQuery)}(function(n){"use strict";return n.effects.define("scale",function(e,t){var f=n(this),i=e.mode,i=parseInt(e.percent,10)||(0===parseInt(e.percent,10)||"effect"!==i?0:100),f=n.extend(!0,{from:n.effects.scaledDimensions(f),to:n.effects.scaledDimensions(f,i,e.direction||"both"),origin:e.origin||["middle","center"]},e);e.fade&&(f.from.opacity=1,f.to.opacity=0),n.effects.effect.size.call(this,f,t)})});