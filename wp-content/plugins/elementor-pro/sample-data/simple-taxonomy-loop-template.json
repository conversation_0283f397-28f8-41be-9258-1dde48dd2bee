{"content": [{"id": "ba28a8c", "settings": [], "elements": [{"id": "59717e2b", "settings": {"title": "Add Your Heading Text Here", "align": "center", "title_color": "#E46EC0", "typography_typography": "custom", "typography_font_family": "Assistant", "typography_font_weight": "700", "typography_text_transform": "uppercase", "text_stroke_text_stroke": {"unit": "px", "size": 0, "sizes": []}, "_padding": {"unit": "%", "top": "0", "right": "4", "bottom": "0", "left": "4", "isLinked": false}, "_padding_widescreen": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_laptop": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_tablet_extra": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_tablet": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile_extra": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "__dynamic__": {"title": "[elementor-tag id=\"fddf42e\" name=\"archive-title\" settings=\"%7B%7D\"]"}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "1f3c48a4", "settings": {"text_columns": "1", "align": "center", "_padding": {"unit": "%", "top": "0", "right": "4", "bottom": "0", "left": "4", "isLinked": false}, "_padding_widescreen": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_laptop": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_tablet_extra": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_tablet": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile_extra": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "__dynamic__": {"editor": "[elementor-tag id=\"76b1db6\" name=\"archive-description\" settings=\"%7B%7D\"]"}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "28993854", "settings": {"_padding": {"unit": "%", "top": "0", "right": "4", "bottom": "0", "left": "4", "isLinked": false}, "_padding_widescreen": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_laptop": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_tablet_extra": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_tablet": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile_extra": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "__dynamic__": {"image": "[elementor-tag id=\"e1425e3\" name=\"woocommerce-category-image-tag\" settings=\"%7B%7D\"]"}, "image": {"id": 69, "url": "http://wordpress-dev.local/wp-content/uploads/2024/03/placeholder.png"}}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "4038b812", "settings": {"width": {"unit": "%", "size": 100, "sizes": []}, "_padding": {"unit": "%", "top": "0", "right": "4", "bottom": "0", "left": "4", "isLinked": false}, "_padding_widescreen": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_laptop": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_tablet_extra": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_tablet": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile_extra": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "__dynamic__": {"image": "[elementor-tag id=\"b6ac506\" name=\"acf-image\" settings=\"%7B%22key%22%3A%22field_65f84be959d4e%3Ataxonomy_image%22%7D\"]"}, "image": {"id": 70, "url": "http://wordpress-dev.local/wp-content/uploads/2024/03/placeholder-1.png"}}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "46ec20a8", "settings": {"align": "center", "text_color": "#473C3C", "typography_typography": "custom", "typography_font_family": "Roboto", "typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "typography_font_weight": "400", "typography_text_transform": "capitalize", "typography_letter_spacing": {"unit": "px", "size": 0.1, "sizes": []}, "typography_word_spacing": {"unit": "px", "size": 11, "sizes": []}, "_padding": {"unit": "%", "top": "0", "right": "4", "bottom": "0", "left": "4", "isLinked": false}, "_padding_widescreen": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_laptop": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_tablet_extra": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_tablet": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile_extra": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "__dynamic__": {"editor": "[elementor-tag id=\"e50714a\" name=\"acf-text\" settings=\"%7B%22key%22%3A%22field_65f84bb059d4d%3Acustom_text%22%7D\"]"}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "255d6f31", "settings": {"text": "Click here", "align": "justify", "typography_typography": "custom", "typography_font_family": "Assistant", "typography_font_size": {"unit": "px", "size": 21, "sizes": []}, "typography_font_weight": "300", "typography_text_transform": "uppercase", "typography_letter_spacing": {"unit": "px", "size": 1.9, "sizes": []}, "typography_word_spacing": {"unit": "px", "size": 8, "sizes": []}, "background_color": "#CE61B2", "_padding": {"unit": "%", "top": "0", "right": "4", "bottom": "0", "left": "4", "isLinked": false}, "_padding_widescreen": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_laptop": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_tablet_extra": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_tablet": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile_extra": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile": {"unit": "%", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "__dynamic__": {"link": "[elementor-tag id=\"74247d7\" name=\"archive-url\" settings=\"%7B%7D\"]"}}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}], "isInner": false, "elType": "container"}], "page_settings": {"preview_id": "1244"}, "version": "0.4", "title": "TaxonomyTemplate", "type": "loop-item"}