/*! elementor-pro - v3.29.0 - 19-05-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[33],{1195:(e,t,s)=>{var i=s(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(s(5012));class NestedCarousel extends elementorModules.frontend.handlers.CarouselBase{getDefaultSettings(){const e=super.getDefaultSettings();return e.selectors.carousel=".e-n-carousel",e.selectors.slidesWrapper=".e-n-carousel > .swiper-wrapper",e}getSwiperSettings(){const e=super.getSwiperSettings(),t=this.getElementSettings(),s=elementorFrontend.config.is_rtl,i=`.elementor-element-${this.getID()}`;return elementorFrontend.isEditMode()&&(delete e.autoplay,e.loop=!1,e.noSwipingSelector=".swiper-slide > .e-con .elementor-element"),"yes"===t.arrows&&(e.navigation={prevEl:s?`${i} .elementor-swiper-button-next`:`${i} .elementor-swiper-button-prev`,nextEl:s?`${i} .elementor-swiper-button-prev`:`${i} .elementor-swiper-button-next`}),this.applySwipeOptions(e),e}async onInit(){this.wrapSlideContent(),super.onInit(...arguments),this.ranElementHandlers=!1}async initSwiper(){const e=elementorFrontend.utils.swiper;this.swiper=await new e(this.elements.$swiperContainer,this.getSwiperSettings()),this.elements.$swiperContainer.data("swiper",this.swiper)}handleElementHandlers(){if(this.ranElementHandlers||!this.swiper)return;const e=Array.from(this.swiper.slides).filter((e=>e.classList.contains(this.swiper.params.slideDuplicateClass)));(0,n.default)(e),this.ranElementHandlers=!0}wrapSlideContent(){if(!elementorFrontend.isEditMode())return;const e=this.getSettings(),t=e.selectors.slideContent.replace(".",""),s=this.$element;let i=1;this.findElement(`${e.selectors.slidesWrapper} > .e-con`).each((function(){const n=jQuery(this),r=n.closest("div").hasClass(t),o=s.find(`${e.selectors.slidesWrapper} > .${t}:nth-child(${i})`);r||o.append(n),i++}))}togglePauseOnHover(e){elementorFrontend.isEditMode()||super.togglePauseOnHover(e)}getChangeableProperties(){return{arrows_position:"arrows_position"}}applySwipeOptions(e){this.isTouchDevice()?(e.touchRatio=1,e.longSwipesRatio=.3,e.followFinger=!0,e.threshold=10):e.shortSwipes=!1}isTouchDevice(){return elementorFrontend.utils.environment.isTouchDevice}async linkContainer(e){const{container:t,index:s,targetContainer:i,action:{type:n}}=e.detail,r=t.view.$el;if(t.model.get("id")===this.$element.data("id")){const{$slides:e}=this.getDefaultElements();let t,o;switch(n){case"move":[t,o]=this.move(r,s,i,e);break;case"duplicate":[t,o]=this.duplicate(r,s,i,e)}void 0!==t&&t.appendChild(o),this.shouldHideNavButtons(r,e),this.updateIndexValues(e);const l=this.swiper&&!this.swiper.destroyed,a=e.length>1;!l&&a?await this.initSwiper():l&&!a&&this.swiper.destroy(!0),this.updateListeners()}}updateListeners(){this.swiper.initialized=!1,this.swiper.init()}move(e,t,s,i){return[i[t],s.view.$el[0]]}duplicate(e,t,s,i){return[i[t+1],s.view.$el[0]]}updateIndexValues(e){e.each(((e,t)=>{const s=e+1;t.setAttribute("data-slide",s)}))}bindEvents(){super.bindEvents(),elementorFrontend.elements.$window.on("elementor/nested-container/atomic-repeater",this.linkContainer.bind(this))}shouldHideNavButtons(e,t){const s=e[0].querySelectorAll(".elementor-swiper-button"),i=1===t.length,n=s[0]?.classList.contains("hide");i!==n&&s.forEach((e=>{e.classList.toggle("hide",i)}))}}t.default=NestedCarousel}}]);