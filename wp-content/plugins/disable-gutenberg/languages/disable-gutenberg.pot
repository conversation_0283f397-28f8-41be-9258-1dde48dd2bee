#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Disable <PERSON><PERSON>\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-24 14:17+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: \n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/"

#: disable-gutenberg.php:227
msgid " or higher, and has been deactivated! "
msgstr ""

#: inc/plugin-features.php:49
msgid "(Classic)"
msgstr ""

#: inc/settings-register.php:52
msgid "ACF Support"
msgstr ""

#: inc/settings-reset.php:38
msgid "and"
msgstr ""

#: inc/settings-reset.php:40
msgid "Apply code"
msgstr ""

#: inc/settings-reset.php:40
msgid "at checkout. Sale ends 6/25/2025."
msgstr ""

#: inc/plugin-features.php:86
msgid "Block Edit"
msgstr ""

#: inc/settings-reset.php:39
msgid "books"
msgstr ""

#: inc/plugin-features.php:96
msgid "Classic Edit"
msgstr ""

#: inc/settings-register.php:45
msgid "Classic Widgets"
msgstr ""

#: inc/settings-register.php:167
msgid "Click here"
msgstr ""

#: disable-gutenberg.php:183
msgid "Click here to rate and review this plugin on WordPress.org"
msgstr ""

#: inc/settings-register.php:20
msgid "Complete Disable"
msgstr ""

#: inc/resources-enqueue.php:33
msgid "Confirm Reset"
msgstr ""

#: inc/settings-reset.php:16
msgid "Default options restored."
msgstr ""

#: inc/settings-register.php:46
msgid "Disable \"Try Gutenberg\" nag (for older versions of WP)"
msgstr ""

#: inc/settings-register.php:45
msgid "Disable Block Widgets and enable Classic Widgets"
msgstr ""

#: inc/settings-register.php:26 inc/settings-register.php:34
msgid "Disable for"
msgstr ""

#. Name of the plugin
msgid "Disable Gutenberg"
msgstr ""

#: inc/settings-register.php:20
msgid "Disable Gutenberg everywhere"
msgstr ""

#: inc/settings-register.php:46
msgid "Disable Nag"
msgstr ""

#: inc/settings-register.php:39
msgid "Disable Post IDs"
msgstr ""

#: inc/settings-register.php:38
msgid "Disable Templates"
msgstr ""

#. Description of the plugin
msgid ""
"Disables Gutenberg Block Editor and restores the Classic Editor and original "
"Edit Post screen. Provides options to enable on specific post types, user "
"roles, and more."
msgstr ""

#: inc/settings-reset.php:111
msgid "Dismiss"
msgstr ""

#: inc/settings-register.php:51
msgid "Display \"Add New (Classic)\" menu link and Classic/Block edit links"
msgstr ""

#: inc/settings-register.php:51
msgid "Display Edit Links"
msgstr ""

#: inc/settings-register.php:48
msgid "Display Whitelist settings"
msgstr ""

#: inc/settings-register.php:243
msgid "Donate via PayPal, credit card, or cryptocurrency"
msgstr ""

#: inc/plugin-features.php:88
#, php-format
msgid "Edit &#8220;%s&#8221; in the Block Editor"
msgstr ""

#: inc/plugin-features.php:98
#, php-format
msgid "Edit &#8220;%s&#8221; in the Classic Editor"
msgstr ""

#: inc/settings-register.php:52
msgid "Enable Custom Fields Meta Box (ACF disables by default),"
msgstr ""

#: inc/settings-register.php:47
msgid "Enable Frontend"
msgstr ""

#: inc/settings-register.php:47
msgid "Enable frontend Gutenberg/block styles"
msgstr ""

#: inc/settings-register.php:131
msgid ""
"Enable this setting to completely disable Gutenberg (and restore the Classic "
"Editor). Or, disable this setting to display more options."
msgstr ""

#: inc/settings-register.php:50
msgid "Gutenberg Menu Item"
msgstr ""

#: inc/settings-register.php:50
msgid "Hide Gutenberg plugin&rsquo;s menu item (for WP &lt; 5.0)"
msgstr ""

#: inc/settings-register.php:49
msgid "Hide this plugin&rsquo;s menu item"
msgstr ""

#: disable-gutenberg.php:178
msgid "Homepage"
msgstr ""

#. URI of the plugin
msgid "https://perishablepress.com/disable-gutenberg/"
msgstr ""

#. Author URI of the plugin
msgid "https://plugin-planet.com/"
msgstr ""

#. Author of the plugin
msgid "Jeff Starr"
msgstr ""

#: inc/settings-register.php:52
msgid "learn more"
msgstr ""

#: disable-gutenberg.php:202
msgid "Like this plugin? Give it a"
msgstr ""

#: inc/settings-reset.php:22
msgid "No changes made to options."
msgstr ""

#: inc/resources-enqueue.php:36
msgid "No, abort mission."
msgstr ""

#: inc/settings-register.php:233
msgid "Please give a 5-star rating! A huge THANK YOU for your support!"
msgstr ""

#: disable-gutenberg.php:228
msgid "Please return to the"
msgstr ""

#: disable-gutenberg.php:177
msgid "Plugin Homepage"
msgstr ""

#: inc/settings-register.php:49
msgid "Plugin Menu Item"
msgstr ""

#: inc/settings-register.php:41
msgid "Post IDs that always should use the Block Editor"
msgstr ""

#: inc/settings-register.php:42
msgid "Post slugs that always should use the Block Editor"
msgstr ""

#: inc/settings-register.php:43
msgid "Post titles that always should use the Block Editor"
msgstr ""

#: inc/settings-register.php:34
msgid "Post Type ="
msgstr ""

#: inc/settings-reset.php:37
msgid "Pro WordPress plugins"
msgstr ""

#: inc/settings-register.php:54
msgid "Rate Plugin"
msgstr ""

#: disable-gutenberg.php:184
msgid "Rate this plugin"
msgstr ""

#: disable-gutenberg.php:226
msgid "requires WordPress "
msgstr ""

#: inc/settings-register.php:53
msgid "Reset Options"
msgstr ""

#: inc/resources-enqueue.php:34
msgid "Restore default options?"
msgstr ""

#: inc/settings-register.php:53 inc/settings-register.php:224
msgid "Restore default plugin options"
msgstr ""

#: inc/settings-register.php:155
msgid "Select the post IDs for which Gutenberg should be disabled."
msgstr ""

#: inc/settings-register.php:143
msgid "Select the post types for which Gutenberg should be disabled."
msgstr ""

#: inc/settings-register.php:161
msgid ""
"Select the posts that always should use the Gutenberg Block Editor. Separate "
"multiple values with commas."
msgstr ""

#: inc/settings-register.php:149
msgid ""
"Select the theme template files for which Gutenberg should be disabled (e.g.,"
" custom-page.php)."
msgstr ""

#: inc/settings-register.php:137
msgid "Select the user roles for which Gutenberg should be disabled."
msgstr ""

#: inc/settings-register.php:39
msgid "Separate multiple post IDs with commas"
msgstr ""

#: inc/settings-register.php:38
msgid "Separate multiple templates with commas"
msgstr ""

#: disable-gutenberg.php:162
msgid "Settings"
msgstr ""

#: inc/settings-register.php:55
msgid "Show Support"
msgstr ""

#: inc/settings-register.php:54 inc/settings-register.php:234
msgid "Show support with a 5-star rating&nbsp;&raquo;"
msgstr ""

#: inc/settings-register.php:55 inc/settings-register.php:244
msgid "Show support with a small donation&nbsp;&raquo;"
msgstr ""

#: disable-gutenberg.php:243 disable-gutenberg.php:249
msgid "Sorry, pal!"
msgstr ""

#: inc/settings-reset.php:35
msgid "Spring Sale!"
msgstr ""

#: inc/settings-reset.php:36
msgid "Take 30% OFF any of our"
msgstr ""

#: inc/settings-register.php:167
msgid ""
"to display more tools and options. Note: these options remain in effect even "
"when hidden on this page."
msgstr ""

#: disable-gutenberg.php:229
msgid "to upgrade WordPress and try again."
msgstr ""

#: inc/settings-register.php:167
msgid "Toggle More Tools"
msgstr ""

#: inc/settings-register.php:26
msgid "User Role ="
msgstr ""

#: inc/settings-register.php:48
msgid "Whitelist Options"
msgstr ""

#: inc/settings-register.php:41
msgid "Whitelist Post IDs"
msgstr ""

#: inc/settings-register.php:42
msgid "Whitelist Post Slugs"
msgstr ""

#: inc/settings-register.php:43
msgid "Whitelist Post Titles"
msgstr ""

#: disable-gutenberg.php:229
msgid "WP Admin Area"
msgstr ""

#: inc/resources-enqueue.php:35
msgid "Yes, make it so."
msgstr ""

#: disable-gutenberg.php:206
msgid "★★★★★ rating&nbsp;&raquo;"
msgstr ""
