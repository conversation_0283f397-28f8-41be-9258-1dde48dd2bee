(()=>{"use strict";var t={n:e=>{var l=e&&e.__esModule?()=>e.default:()=>e;return t.d(l,{a:l}),l},d:(e,l)=>{for(var a in l)t.o(l,a)&&!t.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:l[a]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};const e=window.wp.i18n,l=window.wp.blocks,a=window.wp.blockEditor,r=window.ReactJSXRuntime,o=(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 242.5 239.46",children:[(0,r.jsx)("defs",{children:(0,r.jsx)("clipPath",{id:"clip-path",transform:"translate(1.72)",children:(0,r.jsx)("circle",{className:"cls-1",cx:"119.73",cy:"119.73",r:"116.15",fill:"none"})})}),(0,r.jsx)("g",{id:"Layer_2","data-name":"Layer 2",children:(0,r.jsxs)("g",{id:"Layer_1","data-name":"Layer 1",children:[(0,r.jsxs)("g",{className:"cls-2",clipPath:"url(#clip-path)",children:[(0,r.jsx)("circle",{className:"cls-3",cx:"121.45",cy:"119.73",r:"116.15",fill:"#33c6f4"}),(0,r.jsx)("path",{className:"cls-4",d:"M239.32,167.79c-53.41-24-108.37-91.46-113-94.55s-10.84.77-10.84.77c-3.87-6.19-10.06.77-10.06.77C76.77,123.55.14,170.11.14,170.11S36.94,237.79,122,237.79C208.48,237.79,239.32,167.79,239.32,167.79Z",transform:"translate(1.72)",fill:"#1b447e"}),(0,r.jsx)("path",{className:"cls-5",d:"M67.48,116.58s15.48-7,12.38,4.65-15.48,28.64-11.61,29.41S83,140.58,86.06,142.12s5.42.78,3.87,6.2-3.1,9.29,0,9.29,5.42-7,9.29-13.94,10.06-3.87,12.38-1.55,9.29,15.49,14.71,13.94,8.51-8.52,6.19-24,1.55-20.12,1.55-20.12,4.64-2.32,13.16,8.51,24,27.09,26.31,26.32-10.83-17.8-7.74-19.35,15.48,2.32,21.68,7.74c0,0,2.12,8.87,2.12.36L126.31,73.24,115.47,74l-10.06.77S80.64,111.94,67.48,116.58Z",transform:"translate(1.72)",fill:"#fff"}),(0,r.jsx)("path",{className:"cls-6",d:"M239.32,170.11c-53.41-24-108.37-93.78-113-96.87s-10.84.77-10.84.77c-3.87-6.19-10.06.77-10.06.77C76.77,123.55.14,170.11.14,170.11",transform:"translate(1.72)",fill:"none",stroke:"#221e1f",strokeMiterlimit:"10",strokeWidth:"8px"})]}),(0,r.jsx)("circle",{className:"cls-6",cx:"121.45",cy:"119.73",r:"116.15",fill:"none",stroke:"#1b447e",strokeMiterlimit:"10",strokeWidth:"8px"})]})})]}),s=window.wp.element,c=window.wp.components,n=window.wp.apiFetch;var i=t.n(n);const m=window.wp.url,h=async t=>i()({path:(0,m.addQueryArgs)("/contact-form-7/v1/contact-forms",{posts_per_page:20,orderby:"modified",order:"DESC",...t})}).then((t=>t)),d=t=>{let e="[contact-form-7]";return t.hash?e=e.replace(/\]$/,` id="${t.hash}"]`):t.id&&(e=e.replace(/\]$/,` id="${t.id}"]`)),t.title&&(e=e.replace(/\]$/,` title="${t.title}"]`)),t.htmlId&&(e=e.replace(/\]$/,` html_id="${t.htmlId}"]`)),t.htmlName&&(e=e.replace(/\]$/,` html_name="${t.htmlName}"]`)),t.htmlTitle&&(e=e.replace(/\]$/,` html_title="${t.htmlTitle}"]`)),t.htmlClass&&(e=e.replace(/\]$/,` html_class="${t.htmlClass}"]`)),"raw_form"===t.output&&(e=e.replace(/\]$/,` output="${t.output}"]`)),e},p=t=>{const e=ajaxurl.replace(/\/admin-ajax\.php$/,"/admin.php");return(0,m.addQueryArgs)(e,{page:"wpcf7",post:t.id,action:"edit"})},f={from:[],to:[{type:"block",blocks:["core/shortcode"],transform:t=>{const e=d(t);return(0,l.createBlock)("core/shortcode",{text:e})}}]};(0,l.registerBlockType)("contact-form-7/contact-form-selector",{icon:o,transforms:f,edit:function({attributes:t,setAttributes:l}){const o=t=>t.reduce(((t,e)=>t.set(e.id,e)),new Map),[n,i]=(0,s.useState)((()=>o([])));return(0,s.useEffect)((()=>{h().then((t=>{i(o(t))}))}),[]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(a.InspectorControls,{children:[t.id&&(0,r.jsx)(c.PanelBody,{title:t.title,children:(0,r.jsx)(c.ExternalLink,{href:p(t),children:(0,e.__)("Edit this contact form","contact-form-7")})}),t.id&&(0,r.jsxs)(c.PanelBody,{title:(0,e.__)("Form attributes","contact-form-7"),initialOpen:!1,children:[(0,r.jsx)(c.TextControl,{label:(0,e.__)("ID","contact-form-7"),value:t.htmlId,onChange:t=>l({htmlId:t}),help:(0,e.__)("Used for the id attribute value of the form element.","contact-form-7")}),(0,r.jsx)(c.TextControl,{label:(0,e.__)("Name","contact-form-7"),value:t.htmlName,onChange:t=>l({htmlName:t}),help:(0,e.__)("Used for the name attribute value of the form element.","contact-form-7")}),(0,r.jsx)(c.TextControl,{label:(0,e.__)("Title","contact-form-7"),value:t.htmlTitle,onChange:t=>l({htmlTitle:t}),help:(0,e.__)("Used for the aria-label attribute value of the form element.","contact-form-7")}),(0,r.jsx)(c.TextControl,{label:(0,e.__)("Class","contact-form-7"),value:t.htmlClass,onChange:t=>l({htmlClass:t}),help:(0,e.__)("Used for the class attribute value of the form element.","contact-form-7")})]})]}),(0,r.jsx)("div",{...(0,a.useBlockProps)({className:"components-placeholder",style:{marginTop:"28px",marginBottom:"28px"}}),children:(0,r.jsx)(c.ComboboxControl,{label:(0,e.__)("Select a contact form:","contact-form-7"),options:(t=>{const e=[];for(const[l,a]of t)e.push({value:l,label:a.title});return e})(n),value:t.id,onChange:t=>l({id:parseInt(t),hash:n.get(parseInt(t))?.hash,title:n.get(parseInt(t))?.title}),onFilterValueChange:t=>{h({search:t}).then((t=>{i(o(t))}))}})})]})},save:({attributes:t})=>{const e=d(t);return(0,r.jsx)("div",{...a.useBlockProps.save(),children:e})}})})();