<?php
/**
 * Template for Carousel - Design 1
 *
 * @package WP Slick Slider and Image Carousel
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}
?>
<div class="wpsisac-image-slide">
	<div class="wpsisac-slide-wrap" style="<?php echo esc_attr( $slider_height_css ); ?>">
		<?php if( $sliderurl != '' ) { ?>
			<a href="<?php echo esc_url( $sliderurl ); ?>"><img <?php if( $lazyload ) { ?>data-lazy="<?php echo esc_url( $slider_orig_img ); ?>"<?php } ?> src="<?php echo esc_url( $slider_img ); ?>" alt="<?php the_title_attribute(); ?>" /></a>
		<?php } else { ?>
			<img <?php if( $lazyload ) { ?>data-lazy="<?php echo esc_url( $slider_orig_img ); ?>"<?php } ?> src="<?php echo esc_url( $slider_img ); ?>" alt="<?php the_title_attribute(); ?>" />
		<?php } ?>
	</div>
</div>