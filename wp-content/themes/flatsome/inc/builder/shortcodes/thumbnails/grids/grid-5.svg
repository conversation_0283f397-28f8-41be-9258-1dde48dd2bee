<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="100px" height="70px" viewBox="0 0 100 70" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.8.1 (29687) - http://www.bohemiancoding.com/sketch -->
    <title>grid-5</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="106.265625%" y2="109.895844%" id="linearGradient-1">
            <stop stop-color="#C8EAF4" stop-opacity="0.208021966" offset="0%"></stop>
            <stop stop-color="#3DD0FF" offset="100%"></stop>
        </linearGradient>
        <rect id="path-2" x="0" y="0" width="99.9428898" height="70"></rect>
        <mask id="mask-3" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="99.9428898" height="70" fill="white">
            <use xlink:href="#path-2"></use>
        </mask>
        <mask id="mask-5" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="99.9428898" height="70" fill="white">
            <use xlink:href="#path-2"></use>
        </mask>
        <rect id="path-6" x="32" y="7" width="35" height="55"></rect>
        <mask id="mask-7" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="35" height="55" fill="white">
            <use xlink:href="#path-6"></use>
        </mask>
        <mask id="mask-9" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="35" height="55" fill="white">
            <use xlink:href="#path-6"></use>
        </mask>
        <rect id="path-10" x="71" y="7" width="20" height="26"></rect>
        <mask id="mask-11" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="20" height="26" fill="white">
            <use xlink:href="#path-10"></use>
        </mask>
        <mask id="mask-13" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="20" height="26" fill="white">
            <use xlink:href="#path-10"></use>
        </mask>
        <rect id="path-14" x="71" y="36" width="20" height="26"></rect>
        <mask id="mask-15" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="20" height="26" fill="white">
            <use xlink:href="#path-14"></use>
        </mask>
        <mask id="mask-17" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="20" height="26" fill="white">
            <use xlink:href="#path-14"></use>
        </mask>
        <rect id="path-18" x="8" y="7" width="20" height="55"></rect>
        <mask id="mask-19" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="20" height="55" fill="white">
            <use xlink:href="#path-18"></use>
        </mask>
        <mask id="mask-21" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="20" height="55" fill="white">
            <use xlink:href="#path-18"></use>
        </mask>
    </defs>
    <g id="BannerGrid" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="grid-5">
            <mask id="mask-4" fill="white">
                <use xlink:href="#path-2"></use>
            </mask>
            <g id="BG" stroke="#00A0D2" mask="url(#mask-3)" stroke-width="2" fill-opacity="0.0336574389" fill="#FFFFFF" opacity="0.12943097">
                <use mask="url(#mask-5)" xlink:href="#path-2"></use>
            </g>
            <mask id="mask-8" fill="white">
                <use xlink:href="#path-6"></use>
            </mask>
            <g id="BG" stroke="#00A0D2" mask="url(#mask-7)" stroke-width="2" fill-opacity="0.15" fill="url(#linearGradient-1)">
                <use mask="url(#mask-9)" xlink:href="#path-6"></use>
            </g>
            <mask id="mask-12" fill="white">
                <use xlink:href="#path-10"></use>
            </mask>
            <g id="BG" stroke="#00A0D2" mask="url(#mask-11)" stroke-width="2" fill-opacity="0.15" fill="url(#linearGradient-1)">
                <use mask="url(#mask-13)" xlink:href="#path-10"></use>
            </g>
            <mask id="mask-16" fill="white">
                <use xlink:href="#path-14"></use>
            </mask>
            <g id="BG" stroke="#00A0D2" mask="url(#mask-15)" stroke-width="2" fill-opacity="0.15" fill="url(#linearGradient-1)">
                <use mask="url(#mask-17)" xlink:href="#path-14"></use>
            </g>
            <mask id="mask-20" fill="white">
                <use xlink:href="#path-18"></use>
            </mask>
            <g id="BG" stroke="#00A0D2" mask="url(#mask-19)" stroke-width="2" fill-opacity="0.15" fill="url(#linearGradient-1)">
                <use mask="url(#mask-21)" xlink:href="#path-18"></use>
            </g>
        </g>
    </g>
</svg>