<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="103px" height="70px" viewBox="0 0 103 70" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.8.1 (29687) - http://www.bohemiancoding.com/sketch -->
    <title>button-call-to-action</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="102.941176" height="70"></rect>
        <linearGradient x1="41.669172%" y1="100%" x2="41.669172%" y2="-25.7272907%" id="linearGradient-3">
            <stop stop-color="#00A0D2" offset="0%"></stop>
            <stop stop-color="#3DD0FF" offset="100%"></stop>
        </linearGradient>
        <filter x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" id="filter-4">
            <feMorphology radius="0.5" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <rect id="path-5" x="0" y="-3.19744231e-14" width="78" height="26.9351623" rx="3"></rect>
        <filter x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" id="filter-6">
            <feMorphology radius="0.5" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="Presets-Button" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="button-call-to-action">
            <mask id="mask-2" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <use id="BG" fill="#FFFFFF" xlink:href="#path-1"></use>
            <g id="Rectangle-166-Copy-+-Shape-Copy-18" filter="url(#filter-4)" mask="url(#mask-2)">
                <g transform="translate(13.000000, 21.000000)">
                    <g id="Rectangle-166-Copy" fill="none">
                        <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                        <use stroke="#00A0D2" stroke-width="1" fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-5"></use>
                    </g>
                    <path d="M16.0276875,17 L16.0276875,8.53634375 L18.7268438,8.53634375 C19.2465963,8.53634375 19.6944825,8.58753074 20.0705156,8.68990625 C20.4465488,8.79228176 20.7556394,8.93796781 20.9977969,9.12696875 C21.2399543,9.3159697 21.4191088,9.54729551 21.5352656,9.82095313 C21.6514225,10.0946107 21.7095,10.402717 21.7095,10.7452813 C21.7095,10.9539698 21.6770159,11.1547803 21.6120469,11.3477188 C21.5470778,11.5406572 21.4486413,11.7198117 21.3167344,11.8851875 C21.1848275,12.0505633 21.0194541,12.1982181 20.8206094,12.3281563 C20.6217646,12.4580944 20.3884701,12.5644058 20.1207188,12.6470938 C20.7389093,12.7691569 21.2054984,12.9916234 21.5205,13.3145 C21.8355016,13.6373766 21.993,14.0626224 21.993,14.59025 C21.993,14.9485643 21.9270475,15.2753735 21.7951406,15.5706875 C21.6632337,15.8660015 21.4702981,16.1199677 21.2163281,16.3325938 C20.9623581,16.5452198 20.6512987,16.7096088 20.2831406,16.8257656 C19.9149825,16.9419225 19.4946586,17 19.0221563,17 L16.0276875,17 Z M17.1735,13.1432188 L17.1735,16.0904375 L19.0044375,16.0904375 C19.3312516,16.0904375 19.6117957,16.0530316 19.8460781,15.9782188 C20.0803605,15.9034059 20.2732961,15.7980788 20.4248906,15.6622344 C20.5764851,15.5263899 20.6877184,15.3649541 20.7585938,15.1779219 C20.8294691,14.9908897 20.8649063,14.7851574 20.8649063,14.5607188 C20.8649063,14.1236541 20.7103609,13.7781419 20.4012656,13.5241719 C20.0921703,13.2702019 19.6245969,13.1432188 18.9985313,13.1432188 L17.1735,13.1432188 Z M17.1735,12.3281563 L18.6855,12.3281563 C19.0083766,12.3281563 19.2879363,12.2927191 19.5241875,12.2218438 C19.7604387,12.1509684 19.9563273,12.0525319 20.1118594,11.9265313 C20.2673914,11.8005306 20.3825621,11.647954 20.457375,11.4687969 C20.5321879,11.2896397 20.5695938,11.0937511 20.5695938,10.881125 C20.5695938,10.3849975 20.4199702,10.0207824 20.1207188,9.78846875 C19.8214673,9.55615509 19.3568469,9.44 18.7268438,9.44 L17.1735,9.44 L17.1735,12.3281563 Z M26.9601563,16.0904375 C27.3105955,16.0904375 27.6236236,16.0313756 27.89925,15.91325 C28.1748764,15.7951244 28.4081709,15.6297511 28.5991406,15.417125 C28.7901103,15.2044989 28.9357964,14.9505327 29.0362031,14.6552188 C29.1366099,14.3599048 29.1868125,14.0350643 29.1868125,13.6806875 L29.1868125,8.53634375 L30.3267188,8.53634375 L30.3267188,13.6806875 C30.3267188,14.1689399 30.2489539,14.6217479 30.0934219,15.039125 C29.9378898,15.4565021 29.7154233,15.8177641 29.4260156,16.1229219 C29.1366079,16.4280797 28.7832208,16.6672804 28.3658438,16.8405313 C27.9484667,17.0137821 27.4799088,17.1004063 26.9601563,17.1004063 C26.4404037,17.1004063 25.9718458,17.0137821 25.5544688,16.8405313 C25.1370917,16.6672804 24.7827202,16.4280797 24.4913438,16.1229219 C24.1999673,15.8177641 23.9765164,15.4565021 23.8209844,15.039125 C23.6654523,14.6217479 23.5876875,14.1689399 23.5876875,13.6806875 L23.5876875,8.53634375 L24.7275938,8.53634375 L24.7275938,13.6747813 C24.7275938,14.029158 24.7777964,14.3539985 24.8782031,14.6493125 C24.9786099,14.9446265 25.1242959,15.1985927 25.3152656,15.4112188 C25.5062353,15.6238448 25.7405142,15.7902025 26.0181094,15.9102969 C26.2957045,16.0303912 26.609717,16.0904375 26.9601563,16.0904375 L26.9601563,16.0904375 Z M38.058,8.53634375 L38.058,9.4990625 L35.3234063,9.4990625 L35.3234063,17 L34.1775938,17 L34.1775938,9.4990625 L31.4311875,9.4990625 L31.4311875,8.53634375 L38.058,8.53634375 Z M45.027375,8.53634375 L45.027375,9.4990625 L42.2927813,9.4990625 L42.2927813,17 L41.1469688,17 L41.1469688,9.4990625 L38.4005625,9.4990625 L38.4005625,8.53634375 L45.027375,8.53634375 Z M53.5146563,12.771125 C53.5146563,13.4050657 53.414251,13.9868255 53.2134375,14.5164219 C53.012624,15.0460183 52.7291268,15.5017793 52.3629375,15.8837188 C51.9967482,16.2656582 51.5567369,16.5619521 51.0428906,16.7726094 C50.5290443,16.9832667 49.9610656,17.0885938 49.3389375,17.0885938 C48.7168094,17.0885938 48.1498151,16.9832667 47.6379375,16.7726094 C47.1260599,16.5619521 46.6870331,16.2656582 46.3208438,15.8837188 C45.9546544,15.5017793 45.6711573,15.0460183 45.4703438,14.5164219 C45.2695302,13.9868255 45.169125,13.4050657 45.169125,12.771125 C45.169125,12.1371843 45.2695302,11.5554245 45.4703438,11.0258281 C45.6711573,10.4962317 45.9546544,10.0394863 46.3208438,9.65557813 C46.6870331,9.27166996 47.1260599,8.97340731 47.6379375,8.76078125 C48.1498151,8.54815519 48.7168094,8.44184375 49.3389375,8.44184375 C49.9610656,8.44184375 50.5290443,8.54815519 51.0428906,8.76078125 C51.5567369,8.97340731 51.9967482,9.27166996 52.3629375,9.65557813 C52.7291268,10.0394863 53.012624,10.4962317 53.2134375,11.0258281 C53.414251,11.5554245 53.5146563,12.1371843 53.5146563,12.771125 L53.5146563,12.771125 Z M52.3393125,12.771125 C52.3393125,12.2513724 52.2684382,11.7847833 52.1266875,11.3713438 C51.9849368,10.9579042 51.7841263,10.6084546 51.52425,10.3229844 C51.2643737,10.0375142 50.9493769,9.81800077 50.57925,9.6644375 C50.2091231,9.51087423 49.7956898,9.43409375 49.3389375,9.43409375 C48.8861227,9.43409375 48.4746581,9.51087423 48.1045313,9.6644375 C47.7344044,9.81800077 47.4184232,10.0375142 47.1565781,10.3229844 C46.8947331,10.6084546 46.6929382,10.9579042 46.5511875,11.3713438 C46.4094368,11.7847833 46.3385625,12.2513724 46.3385625,12.771125 C46.3385625,13.2908776 46.4094368,13.7564823 46.5511875,14.1679531 C46.6929382,14.5794239 46.8947331,14.9278892 47.1565781,15.2133594 C47.4184232,15.4988296 47.7344044,15.7173586 48.1045313,15.8689531 C48.4746581,16.0205476 48.8861227,16.0963438 49.3389375,16.0963438 C49.7956898,16.0963438 50.2091231,16.0205476 50.57925,15.8689531 C50.9493769,15.7173586 51.2643737,15.4988296 51.52425,15.2133594 C51.7841263,14.9278892 51.9849368,14.5794239 52.1266875,14.1679531 C52.2684382,13.7564823 52.3393125,13.2908776 52.3393125,12.771125 L52.3393125,12.771125 Z M55.6704375,8.53634375 C55.772813,8.53634375 55.8486091,8.5491405 55.8978281,8.57473438 C55.9470471,8.60032825 56.0031559,8.65249961 56.0661563,8.73125 L60.9683438,15.11 C60.9565312,15.0076245 60.9486563,14.9082036 60.9447188,14.8117344 C60.9407812,14.7152651 60.9388125,14.6217505 60.9388125,14.5311875 L60.9388125,8.53634375 L61.942875,8.53634375 L61.942875,17 L61.3640625,17 C61.2734995,17 61.1977034,16.9842502 61.1366719,16.95275 C61.0756403,16.9212498 61.015594,16.8680941 60.9565313,16.7932813 L56.06025,10.4204375 C56.068125,10.5188755 56.0740312,10.6153433 56.0779688,10.7098438 C56.0819063,10.8043442 56.083875,10.8909684 56.083875,10.9697188 L56.083875,17 L55.0798125,17 L55.0798125,8.53634375 L55.6704375,8.53634375 Z" id="CALL-TO-ACTION" fill="#FFFFFF" fill-rule="evenodd"></path>
                </g>
            </g>
        </g>
    </g>
</svg>