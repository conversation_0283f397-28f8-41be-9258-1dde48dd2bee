<?php

/**
 * Editor mode.
 *
 * @return  tring
 */
function ux_builder_mode() {
  return array_key_exists( 'ux_builder', $_GET)
    ? $_GET['ux_builder']
    : 'frontend';
}

/**
 * Is the builder active?
 *
 * @return  boolean
 */
function ux_builder_is_active() {
  if ( defined( 'FLATSOME_TESTSUITE' ) ) {
    return true;
  }

  return (
    ux_builder_is_iframe() ||
    ux_builder_is_editor() ||
    ux_builder_is_doing_shortcode() ||
    ux_builder_is_doing_GET_ajax() ||
    ux_builder_is_doing_POST_ajax() ||
    ux_builder_is_saving()
  ) && is_user_logged_in();
}

/**
 * Is this the editor?
 *
 * @return  boolean
 */
function ux_builder_is_editor() {
  return (
    array_key_exists( 'app', $_GET ) &&
    array_key_exists( 'type', $_GET ) &&
    $_GET['app'] === 'uxbuilder'
  );
}

/**
 * Is this the iframe?
 *
 * @return  boolean
 */
function ux_builder_is_iframe() {
  return array_key_exists( 'uxb_iframe', $_GET );
}

/**
 * The editor is rendering a shortcode template.
 *
 * @return  boolean
 */
function ux_builder_is_doing_shortcode() {
  return array_key_exists( 'ux_builder_action', $_POST ) &&
    $_POST['ux_builder_action'] == 'do_shortcode';
}

/**
 * The editor is doing some ajax stuff.
 *
 * @return  boolean
 */
function ux_builder_is_doing_GET_ajax() {
  return array_key_exists( 'action', $_GET ) &&
    strpos( $_GET['action'], 'ux_builder' ) !== false;
}

/**
 * The editor is doing some ajax stuff.
 *
 * @return  boolean
 */
function ux_builder_is_doing_POST_ajax() {
  return array_key_exists( 'action', $_POST ) &&
    strpos( $_POST['action'], 'ux_builder' ) !== false;
}

/**
 * The editor is saving.
 *
 * @return  boolean
 */
function ux_builder_is_saving() {
  return array_key_exists( 'action', $_POST ) &&
    $_POST['action'] == 'ux_builder_save';
}
