<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="100px" height="72px" viewBox="0 0 100 72" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.8.1 (29687) - http://www.bohemiancoding.com/sketch -->
    <title>icon-outline</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="100" height="71.4285714" rx="4"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="100" height="71.4285714" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="100" height="71.4285714" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <rect id="path-5" x="23.3075435" y="8.28125" width="52.9293346" height="53.4462226" rx="26.4646673"></rect>
        <mask id="mask-6" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="52.9293346" height="53.4462226" fill="white">
            <use xlink:href="#path-5"></use>
        </mask>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icon-outline">
            <mask id="mask-3" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask" stroke="#3498DB" mask="url(#mask-2)" stroke-width="2" fill-opacity="0.01" fill="#00A0D2">
                <use mask="url(#mask-4)" xlink:href="#path-1"></use>
            </g>
            <g id="Rectangle-166-Copy-4" mask="url(#mask-3)" stroke="#3498DB" stroke-width="2" fill="#3498DB" fill-opacity="0.127858922">
                <use mask="url(#mask-6)" xlink:href="#path-5"></use>
            </g>
            <path d="M60.0451456,32.9627956 C60.0451456,34.6529341 59.3168644,36.1727436 58.1571251,37.2260274 L58.1665182,37.2260274 L51.9044269,43.4881186 C51.2782178,44.1143278 50.6520087,44.7405369 50.0257996,44.7405369 C49.3995904,44.7405369 48.7733813,44.1143278 48.1471722,43.4881186 L41.8850809,37.2260274 L41.8944741,37.2260274 C40.7347348,36.1727436 40.0064535,34.6529341 40.0064535,32.9627956 C40.0064535,29.7835319 42.5839303,27.2066814 45.7625678,27.2066814 C47.4527063,27.2066814 48.9725158,27.9349626 50.0257996,29.0947019 C51.0790833,27.9349626 52.5988929,27.2066814 54.2890313,27.2066814 C57.468295,27.2066814 60.0451456,29.7841581 60.0451456,32.9627956 L60.0451456,32.9627956 Z" id="Shape-Copy-14" fill="#3498DB" mask="url(#mask-3)"></path>
        </g>
    </g>
</svg>