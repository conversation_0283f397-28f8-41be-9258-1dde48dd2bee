# Copyright (C) 2024 UX-Themes
# This file is distributed under the https://themeforest.net/licenses.
msgid ""
msgstr ""
"Project-Id-Version: l10n-flatsome\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/theme/flatsome\n"
"Last-Translator: \n"
"Language-Team: Swedish\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-12-16T13:56:13+00:00\n"
"PO-Revision-Date: 2024-12-16 14:00\n"
"X-Generator: WP-CLI 2.10.0\n"
"X-Domain: flatsome\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: l10n-flatsome\n"
"X-Crowdin-Project-ID: 561865\n"
"X-Crowdin-Language: sv-SE\n"
"X-Crowdin-File: /master/flatsome/languages/flatsome.pot\n"
"X-Crowdin-File-ID: 2957\n"
"Language: sv_SE\n"

#. Theme Name of the theme
#: style.css
#: inc/admin/admin-notice.php:75
msgid "Flatsome"
msgstr ""

#. Theme URI of the theme
#: style.css
msgid "http://flatsome.uxthemes.com"
msgstr ""

#. Description of the theme
#: style.css
msgid "Multi-Purpose Responsive WooCommerce Theme"
msgstr ""

#. Author of the theme
#: style.css
msgid "UX-Themes"
msgstr ""

#. Author URI of the theme
#: style.css
msgid "https://uxthemes.com"
msgstr ""

#: 404.php:23
msgid "Oops! That page can&rsquo;t be found."
msgstr "Attans! Den sidan finns faktiskt inte."

#: 404.php:26
msgid "It looks like nothing was found at this location. Maybe try one of the links below or a search?"
msgstr "Exakt inget kunde hittas här. Kanske kan du prova nån av länkarna i menyn eller söka lite?"

#. translators: %1$s: Comment count, %2$s: Comment title
#: comments.php:34
msgctxt "comments title"
msgid "One thought on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s thoughts on &ldquo;%2$s&rdquo;"
msgstr[0] "En tanke om &ldquo;%2$s&rdquo;"
msgstr[1] "%1$s tankar om &ldquo;%2$s&rdquo;"

#: comments.php:49
msgid "Comment navigation"
msgstr ""

#: comments.php:51
msgid "Older Comments"
msgstr ""

#: comments.php:52
msgid "Newer Comments"
msgstr ""

#: comments.php:63
msgid "Comments are closed."
msgstr ""

#: header.php:25
msgid "Skip to content"
msgstr ""

#: image.php:27
msgid "Published <span class=\"entry-date\"><time class=\"entry-date\" datetime=\"%1$s\">%2$s</time></span> at <a href=\"%3$s\" title=\"Link to full-size image\">%4$s &times; %5$s</a> in <a href=\"%6$s\" title=\"Return to %7$s\" rel=\"gallery\">%8$s</a>"
msgstr ""

#: image.php:39
#: image.php:109
#: inc/structure/structure-posts.php:168
#: inc/structure/structure-posts.php:197
msgid "Edit"
msgstr ""

#: image.php:101
msgid "<a class=\"comment-link\" href=\"#respond\" title=\"Post a comment\">Post a comment</a> or leave a trackback: <a class=\"trackback-link\" href=\"%s\" title=\"Trackback URL for your post\" rel=\"trackback\">Trackback URL</a>."
msgstr ""

#: image.php:103
msgid "Comments are closed, but you can leave a trackback: <a class=\"trackback-link\" href=\"%s\" title=\"Trackback URL for your post\" rel=\"trackback\">Trackback URL</a>."
msgstr ""

#: image.php:105
msgid "Trackbacks are closed, but you can <a class=\"comment-link\" href=\"#respond\" title=\"Post a comment\">post a comment</a>."
msgstr ""

#: image.php:107
msgid "Both comments and trackbacks are currently closed."
msgstr ""

#: image.php:114
msgid "<span class=\"meta-nav\">&larr;</span> Previous"
msgstr ""

#: image.php:115
msgid "Next <span class=\"meta-nav\">&rarr;</span>"
msgstr ""

#: inc/admin/admin-bar.php:94
msgid "Activate Theme"
msgstr ""

#: inc/admin/admin-notice.php:21
msgid "<a href=\"%s\">Please enter your purchase code</a> to activate Flatsome and get one-click updates."
msgstr ""

#: inc/admin/admin-notice.php:32
msgid "Flatsome issues"
msgstr ""

#: inc/admin/admin-notice.php:38
#: inc/functions/function-site-health.php:53
msgid "Manage registration"
msgstr ""

#: inc/admin/admin-notice.php:43
#: inc/admin/envato_setup/envato_setup.php:2019
#: inc/functions/function-site-health.php:58
#: template-parts/admin/envato/register-form.php:94
msgid "Manage your licenses"
msgstr ""

#. translators: %1$s: Theme name, %2$s: The URL to the status page.
#: inc/admin/admin-notice.php:79
msgid "<strong>Your theme (%1$s) contains outdated copies of some Flatsome template files.</strong> These files may need updating to ensure they are compatible with the current version of Flatsome. Suggestions:"
msgstr ""

#: inc/admin/admin-notice.php:82
msgid "If you copied over a template file to change something, then you will need to copy the new version of the template and apply your changes again."
msgstr ""

#: inc/admin/admin-notice.php:83
msgid "If you are unfamiliar with code/templates and resolving potential conflicts, reach out to a developer for assistance."
msgstr ""

#: inc/admin/admin-notice.php:86
msgid "Learn more about templates"
msgstr ""

#: inc/admin/admin-notice.php:89
msgid "View affected templates"
msgstr ""

#: inc/admin/advanced/functions/functions.facebook.php:162
msgid "Connect Instagram Business accounts"
msgstr ""

#: inc/admin/advanced/functions/functions.facebook.php:169
#: inc/admin/advanced/functions/functions.facebook.php:178
msgid "Okay"
msgstr ""

#: inc/admin/advanced/functions/functions.facebook.php:174
msgid "No associated Instagram Business account was found for your Facebook user."
msgstr ""

#: inc/admin/advanced/functions/functions.facebook.php:229
msgid "Cancel"
msgstr ""

#: inc/admin/advanced/functions/functions.facebook.php:232
msgid "Connect"
msgstr ""

#: inc/admin/advanced/functions/functions.php:23
msgid "Super Admin"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:89
msgid "Flatsome menu item options"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:93
msgid "Menu dropdown"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:96
msgid "Design"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:98
msgid "Default"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:99
msgid "Default (custom size)"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:100
msgid "Container width"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:101
msgid "Full width"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:104
msgid "Select dropdown design."
msgstr ""

#: inc/admin/backend/menu/class-menu.php:108
#: inc/admin/backend/menu/class-menu.php:159
#: inc/admin/options/header/options-header-nav-vertical.php:55
#: inc/admin/options/header/options-header-nav-vertical.php:146
msgid "Width"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:114
msgid "Height (optional)"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:120
#: inc/widgets/widget-blocks.php:88
msgid "UX Block"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:132
msgid "Reveal"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:134
msgid "On hover"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:135
msgid "On click"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:141
msgid "Menu icon"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:144
msgid "Icon type"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:146
msgid "Media library"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:147
msgid "Custom content"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:163
#: inc/admin/options/header/options-header-nav-vertical.php:41
msgid "Height"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:166
msgid "Icons default (empty) to 20x20."
msgstr ""

#: inc/admin/backend/menu/class-menu.php:171
msgid "Markup"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:174
msgid "Add any HTML, SVG or shortcode here."
msgstr ""

#: inc/admin/backend/menu/class-menu.php:245
#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:237
msgid "Select image"
msgstr ""

#: inc/admin/backend/menu/class-menu.php:250
#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:238
msgid "Remove"
msgstr ""

#: inc/admin/classes/class-features.php:30
msgid "Features"
msgstr ""

#: inc/admin/classes/class-features.php:37
msgid "Experimental features"
msgstr ""

#: inc/admin/classes/class-features.php:81
msgid "There are currently no experimental features in development."
msgstr ""

#: inc/admin/classes/class-features.php:83
msgid "Features that are testable while they're in development. These features are likely to change, so avoid using them in production."
msgstr ""

#. translators: %s: PHP version.
#: inc/admin/classes/class-features.php:132
msgid "This feature requires PHP version %s or higher."
msgstr ""

#. translators: %s: WordPress version.
#: inc/admin/classes/class-features.php:136
msgid "This feature requires WordPress version %s or higher."
msgstr ""

#: inc/admin/classes/class-status.php:559
#: inc/admin/classes/class-status.php:587
msgid "Theme"
msgstr ""

#: inc/admin/classes/class-status.php:560
#: inc/admin/classes/class-status.php:700
msgid "WordPress environment"
msgstr ""

#: inc/admin/classes/class-status.php:561
#: inc/admin/classes/class-status.php:812
msgid "Server environment"
msgstr ""

#: inc/admin/classes/class-status.php:562
#: inc/admin/classes/class-status.php:894
#: inc/functions/function-site-health.php:31
msgid "Security"
msgstr ""

#: inc/admin/classes/class-status.php:563
#: inc/admin/classes/class-status.php:950
msgid "Database"
msgstr ""

#: inc/admin/classes/class-status.php:564
#: inc/admin/classes/class-status.php:988
msgid "Templates"
msgstr ""

#: inc/admin/classes/class-status.php:568
msgid "More details"
msgstr ""

#: inc/admin/classes/class-status.php:593
msgid "Name"
msgstr ""

#: inc/admin/classes/class-status.php:595
msgid "The name of the current active theme."
msgstr ""

#: inc/admin/classes/class-status.php:600
msgid "Version"
msgstr ""

#: inc/admin/classes/class-status.php:602
msgid "The version of the current active theme."
msgstr ""

#: inc/admin/classes/class-status.php:607
msgid "Author URL"
msgstr ""

#: inc/admin/classes/class-status.php:609
msgid "The theme developers URL."
msgstr ""

#: inc/admin/classes/class-status.php:614
msgid "Registered"
msgstr ""

#: inc/admin/classes/class-status.php:616
msgid "Displays whether or not the theme is registered."
msgstr ""

#: inc/admin/classes/class-status.php:623
msgid "You should register Flatsome with a purchase code"
msgstr ""

#: inc/admin/classes/class-status.php:629
msgid "Child theme"
msgstr ""

#: inc/admin/classes/class-status.php:631
msgid "Displays whether or not the current active theme is a child theme."
msgstr ""

#. translators: %s: Docs link.
#: inc/admin/classes/class-status.php:639
msgid "If you are modifying Flatsome on the parent theme we recommend using a child theme. See: <a href=\"%s\" target=\"_blank\">How to create a child theme</a>"
msgstr ""

#: inc/admin/classes/class-status.php:645
msgid "Release channel"
msgstr ""

#: inc/admin/classes/class-status.php:647
msgid "Which updates to receive on this site."
msgstr ""

#: inc/admin/classes/class-status.php:661
msgid "Parent theme name"
msgstr ""

#: inc/admin/classes/class-status.php:663
msgid "The name of the parent theme."
msgstr ""

#: inc/admin/classes/class-status.php:668
msgid "Parent theme version"
msgstr ""

#: inc/admin/classes/class-status.php:670
msgid "The installed version of the parent theme."
msgstr ""

#: inc/admin/classes/class-status.php:675
msgid "Parent theme author URL"
msgstr ""

#: inc/admin/classes/class-status.php:677
msgid "The parent theme developers URL."
msgstr ""

#: inc/admin/classes/class-status.php:706
msgid "WordPress version"
msgstr ""

#: inc/admin/classes/class-status.php:708
msgid "The installed WordPress version (indicates the fulfillment of the minimum required version)."
msgstr ""

#. translators: %s: The minimum required WP version number.
#: inc/admin/classes/class-status.php:717
msgid "The theme requires WordPress version %s or above."
msgstr ""

#: inc/admin/classes/class-status.php:729
msgid "WooCommerce version"
msgstr ""

#: inc/admin/classes/class-status.php:731
msgid "The installed WooCommerce version (indicates the fulfillment of the minimum required version)."
msgstr ""

#. translators: %s: The minimum required WC version number.
#: inc/admin/classes/class-status.php:740
msgid "The theme requires WooCommerce version %s or above."
msgstr ""

#: inc/admin/classes/class-status.php:752
msgid "WordPress memory limit"
msgstr ""

#: inc/admin/classes/class-status.php:754
msgid "The maximum amount of memory (RAM) that your site can use at one time."
msgstr ""

#. translators: %1$s: Memory limit, %2$s: Docs link.
#: inc/admin/classes/class-status.php:761
msgid "%1$s - We recommend setting memory to at least 256MB. See: %2$s"
msgstr ""

#. translators: %1$s: Memory limit, %2$s: Docs link.
#: inc/admin/classes/class-status.php:761
msgid "Increasing memory allocated to PHP"
msgstr ""

#: inc/admin/classes/class-status.php:768
msgid "WordPress image sizes"
msgstr ""

#: inc/admin/classes/class-status.php:770
msgid "The image sizes that are registered on this site."
msgstr ""

#: inc/admin/classes/class-status.php:818
msgid "Server info"
msgstr ""

#: inc/admin/classes/class-status.php:819
msgid "Information about the web server that is currently hosting your site."
msgstr ""

#: inc/admin/classes/class-status.php:823
msgid "PHP version"
msgstr ""

#: inc/admin/classes/class-status.php:825
msgid "The version of PHP installed on your hosting server."
msgstr ""

#. translators: %s: The recommended PHP version number.
#: inc/admin/classes/class-status.php:837
msgid "We recommend using PHP version %s or above for greater performance and security."
msgstr ""

#: inc/admin/classes/class-status.php:848
msgid "PHP post max size"
msgstr ""

#: inc/admin/classes/class-status.php:850
msgid "The largest filesize that can be contained in one post."
msgstr ""

#: inc/admin/classes/class-status.php:855
msgid "PHP time limit"
msgstr ""

#: inc/admin/classes/class-status.php:857
msgid "The amount of time (in seconds) that your site will spend on a single operation before timing out (to avoid server lockups)."
msgstr ""

#: inc/admin/classes/class-status.php:862
msgid "PHP max input variables"
msgstr ""

#: inc/admin/classes/class-status.php:864
msgid "The maximum number of variables your server can use for a single function to avoid overloads."
msgstr ""

#: inc/admin/classes/class-status.php:870
msgid "Max upload size"
msgstr ""

#: inc/admin/classes/class-status.php:872
msgid "The largest filesize that can be uploaded to your WordPress installation."
msgstr ""

#: inc/admin/classes/class-status.php:900
msgid "Secure connection (HTTPS)"
msgstr ""

#: inc/admin/classes/class-status.php:902
msgid "Is the connection to your site secure?"
msgstr ""

#. translators: %s: Docs link.
#: inc/admin/classes/class-status.php:911
msgid "Your site is not using HTTPS. <a href=\"%s\" target=\"_blank\">Learn more about HTTPS and SSL Certificates</a>."
msgstr ""

#: inc/admin/classes/class-status.php:918
msgid "Hide errors from visitors"
msgstr ""

#: inc/admin/classes/class-status.php:920
msgid "Error messages can contain sensitive information about your site environment. These should be hidden from untrusted visitors."
msgstr ""

#: inc/admin/classes/class-status.php:927
msgid "Error messages should not be shown to visitors."
msgstr ""

#: inc/admin/classes/class-status.php:956
msgid "Flatsome database version"
msgstr ""

#: inc/admin/classes/class-status.php:958
msgid "The database version for Flatsome. This should be the same as the parent theme version."
msgstr ""

#: inc/admin/classes/class-status.php:965
msgid "This should be the same as the parent theme version."
msgstr ""

#: inc/admin/classes/class-status.php:994
msgid "Overrides"
msgstr ""

#: inc/admin/classes/class-status.php:995
msgid "This section shows any files that are overriding the default Flatsome templates."
msgstr ""

#. translators: %1$s: Template name, %2$s: Template version, %3$s: Core version.
#: inc/admin/classes/class-status.php:1008
msgid "%1$s version %2$s is out of date. The core version is %3$s"
msgstr ""

#: inc/admin/envato_setup/envato_setup.php:2012
msgid "Register Theme"
msgstr ""

#: inc/admin/envato_setup/envato_setup.php:2037
msgid "Continue"
msgstr ""

#: inc/admin/envato_setup/envato_setup.php:2038
msgid "Unregister"
msgstr ""

#: inc/admin/envato_setup/envato_setup.php:2040
msgid "Register"
msgstr ""

#: inc/admin/gutenberg/class-gutenberg.php:119
msgid "Edit with UX Builder"
msgstr ""

#: inc/admin/options/blog/options-blog-single.php:85
msgid "Featured image"
msgstr ""

#: inc/admin/options/blog/options-blog-single.php:101
msgid "Share icons"
msgstr ""

#: inc/admin/options/blog/options-blog-single.php:109
msgid "Meta"
msgstr ""

#: inc/admin/options/blog/options-blog-single.php:117
msgid "Blog author box"
msgstr ""

#: inc/admin/options/blog/options-blog-single.php:125
msgid "Next/Prev navigation"
msgstr ""

#: inc/admin/options/blog/options-blog-single.php:133
msgid "HTML after blog posts"
msgstr ""

#: inc/admin/options/header/options-header-cart.php:115
msgid "Show quantity input"
msgstr ""

#: inc/admin/options/header/options-header-cart.php:125
#: inc/admin/options/shop/options-shop-cart-checkout.php:81
#: inc/admin/options/shop/options-shop-cart-checkout.php:258
msgid "Show free shipping"
msgstr ""

#: inc/admin/options/header/options-header-cart.php:156
#: inc/admin/options/styles/options-lightbox.php:30
msgid "Drawer width"
msgstr ""

#: inc/admin/options/header/options-header-cart.php:165
msgid "Sticky footer"
msgstr ""

#: inc/admin/options/header/options-header-cart.php:175
msgid "Show cross sells"
msgstr ""

#: inc/admin/options/header/options-header-logo.php:106
msgid "Logo link"
msgstr ""

#: inc/admin/options/header/options-header-logo.php:107
msgid "Custom logo link (defaults to home page link if empty)."
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:100
msgid "Menu item behavior"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:101
msgid "Click behavior for menu items with a submenu"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:106
msgid "Open link"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:107
msgid "Toggle submenu"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:114
msgid "Submenu effect"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:119
msgid "Accordion"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:120
msgid "Slide"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:134
msgid "Submenu levels"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:139
msgid "1 level"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:140
msgid "2 levels"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:159
msgid "Top content"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:168
msgid "Tabs"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:172
msgid "None"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:173
msgid "2 Tabs"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:180
msgid "Tab 1 text"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:195
msgid "Menu elements"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:206
msgid "Tab 2 text"
msgstr ""

#: inc/admin/options/header/options-header-mobile.php:221
msgid "Menu elements tab 2"
msgstr ""

#: inc/admin/options/header/options-header-nav-vertical.php:9
#: inc/functions/function-setup.php:120
msgid "Vertical Menu"
msgstr ""

#: inc/admin/options/header/options-header-nav-vertical.php:28
msgid "Icon"
msgstr ""

#: inc/admin/options/header/options-header-nav-vertical.php:69
msgid "Tag line"
msgstr ""

#: inc/admin/options/header/options-header-nav-vertical.php:78
msgid "Text"
msgstr ""

#: inc/admin/options/header/options-header-nav-vertical.php:87
#: inc/admin/options/header/options-header-nav-vertical.php:201
msgid "Text base color"
msgstr ""

#: inc/admin/options/header/options-header-nav-vertical.php:101
msgid "Color"
msgstr ""

#: inc/admin/options/header/options-header-nav-vertical.php:111
#: inc/admin/options/header/options-header-nav-vertical.php:161
msgid "Background color"
msgstr ""

#: inc/admin/options/header/options-header-nav-vertical.php:128
msgid "Keep open on front page"
msgstr ""

#: inc/admin/options/header/options-header-nav-vertical.php:137
msgid "Add shadow"
msgstr ""

#: inc/admin/options/header/options-header-nav-vertical.php:178
msgid "Divider"
msgstr ""

#: inc/admin/options/header/options-header-nav-vertical.php:187
msgid "Nav height"
msgstr ""

#: inc/admin/options/header/options-header-nav-vertical.php:215
msgid "Nav color"
msgstr ""

#: inc/admin/options/header/options-header-nav-vertical.php:225
msgid "Nav color :hover"
msgstr ""

#: inc/admin/options/header/options-header-nav-vertical.php:235
msgid "Nav background color :hover"
msgstr ""

#: inc/admin/options/notifications/options-notifications.php:140
#: inc/extensions/flatsome-cookie-notice/flatsome-cookie-notice.php:39
msgid "This site uses cookies to offer you a better browsing experience. By browsing this website, you agree to our use of cookies."
msgstr ""

#: inc/admin/options/portfolio/options-portfolio.php:76
msgid "Show share icons"
msgstr ""

#: inc/admin/options/shop/options-shop-cart-checkout.php:65
msgid "Boxed Shipping labels"
msgstr ""

#: inc/admin/options/shop/options-shop-cart-checkout.php:73
msgid "Show shipping estimate destination"
msgstr ""

#: inc/admin/options/shop/options-shop-category.php:261
msgid "Show result count"
msgstr ""

#: inc/admin/options/shop/options-shop-category.php:270
msgid "Show catalog ordering"
msgstr ""

#: inc/admin/options/shop/options-shop-category.php:766
#: inc/admin/options/shop/options-shop-product-page.php:488
msgid "Color :selected"
msgstr ""

#: inc/admin/options/shop/options-shop-product-page.php:40
#: inc/woocommerce/structure-wc-category-page.php:141
#: inc/woocommerce/structure-wc-product-page-fields.php:22
msgid "Custom product layout"
msgstr ""

#: inc/admin/options/shop/options-shop-product-page.php:449
msgid "Layout"
msgstr ""

#: inc/admin/options/shop/options-shop-product-page.php:461
msgid "Tooltip"
msgstr ""

#: inc/admin/options/shop/options-shop-product-page.php:462
msgid "Show a tooltip with the term or term description."
msgstr ""

#: inc/admin/options/shop/options-shop-product-page.php:470
msgid "Inactive out of stock"
msgstr ""

#: inc/admin/options/shop/options-shop-product-page.php:471
msgid "Show out of stock items as inactive."
msgstr ""

#: inc/admin/options/shop/options-shop-product-page.php:479
msgid "Disable deselection"
msgstr ""

#: inc/admin/options/social/options-social.php:75
#: inc/admin/options/social/options-social.php:153
#: inc/shortcodes/follow.php:187
msgctxt "social media"
msgid "X"
msgstr ""

#: inc/admin/options/styles/options-colors.php:144
msgid "Tooltip colors"
msgstr ""

#: inc/admin/options/styles/options-colors.php:150
msgid "Tooltip color"
msgstr ""

#: inc/admin/options/styles/options-colors.php:159
msgid "Tooltip background color"
msgstr ""

#: inc/admin/panel/sections/tab-activate.php:12
msgid "Theme registration"
msgstr ""

#: inc/admin/panel/sections/tab-activate.php:17
msgid "Status"
msgstr ""

#: inc/classes/class-flatsome-base-registration.php:49
#: inc/classes/class-flatsome-base-registration.php:58
#: inc/classes/class-flatsome-base-registration.php:67
#: inc/classes/class-flatsome-base-registration.php:77
msgid "Not allowed."
msgstr ""

#. translators: %s: Time left.
#: inc/classes/class-flatsome-envato-admin.php:152
msgid "Please try again in %s."
msgstr ""

#: inc/classes/class-flatsome-envato-admin.php:192
msgid "You must agree to the Envato License Terms."
msgstr ""

#: inc/classes/class-flatsome-registration.php:34
msgid "No purchase code provided."
msgstr ""

#: inc/classes/class-flatsome-registration.php:36
msgid "The provided value seems to be a token. Please register with a purchase code instead."
msgstr ""

#: inc/classes/class-flatsome-registration.php:38
msgid "Invalid purchase code."
msgstr ""

#: inc/classes/class-flatsome-registration.php:55
msgid "Your site is registered. But the purchase code could not be verified at the moment."
msgstr ""

#: inc/classes/class-flatsome-registration.php:105
#: inc/classes/class-flatsome-registration.php:148
msgid "No purchase code."
msgstr ""

#: inc/classes/class-flatsome-registration.php:128
msgid "No version received."
msgstr ""

#: inc/classes/class-flatsome-registration.php:132
msgid "Invalid version received."
msgstr ""

#: inc/classes/class-flatsome-registration.php:159
msgid "No URL received."
msgstr ""

#: inc/classes/class-flatsome-registration.php:163
msgid "Invalid URL received."
msgstr ""

#: inc/classes/class-flatsome-relay.php:230
#: inc/extensions/flatsome-infinite-scroll/templates/button.php:2
msgid "Load more"
msgstr ""

#: inc/classes/class-flatsome-relay.php:241
#: inc/classes/class-flatsome-relay.php:289
#: inc/structure/structure-posts.php:303
#: woocommerce/loop/pagination.php:58
#: woocommerce/single-product-reviews.php:67
msgid "Previous"
msgstr ""

#: inc/classes/class-flatsome-relay.php:246
#: inc/classes/class-flatsome-relay.php:288
#: inc/structure/structure-posts.php:302
#: woocommerce/loop/pagination.php:57
#: woocommerce/single-product-reviews.php:66
msgid "Next"
msgstr ""

#: inc/classes/class-flatsome-wupdates-registration.php:65
#: inc/classes/class-flatsome-wupdates-registration.php:75
msgid "Purchase code not verified."
msgstr ""

#. translators: %d: The status code.
#: inc/classes/class-uxthemes-api.php:78
msgid "Sorry, an error occurred while accessing the API. Error %d"
msgstr ""

#: inc/classes/class-uxthemes-api.php:118
msgid "Your purchase code is malformed."
msgstr ""

#: inc/classes/class-uxthemes-api.php:121
msgid "Sorry, an error occurred. Please try again."
msgstr ""

#: inc/classes/class-uxthemes-api.php:124
msgid "Flatsome was unable to get the latest version. Your site might have changed domain after you registered it."
msgstr ""

#: inc/classes/class-uxthemes-api.php:129
msgid "Flatsome was unable to get the latest version because the purchase code has not been verified yet. Please re-register it in order to receive updates."
msgstr ""

#: inc/classes/class-uxthemes-api.php:134
msgid "The purchase code is malformed or does not belong to a Flatsome sale."
msgstr ""

#. translators: %s: License manager link attributes.
#: inc/classes/class-uxthemes-api.php:138
msgid "The registration was not found for <a%s>your account</a>. It was only deleted on this site."
msgstr ""

#. translators: %s: License manager link attributes.
#: inc/classes/class-uxthemes-api.php:142
msgid "Flatsome was unable to get the latest version. Your registration might have been deleted from <a%s>your account</a>."
msgstr ""

#: inc/classes/class-uxthemes-api.php:145
msgid "Flatsome was unable to get the latest version. Your purchase code is malformed."
msgstr ""

#. translators: %s: License manager link attributes.
#: inc/classes/class-uxthemes-api.php:151
msgid "Your purchase code has been used on too many sites. Please go to <a%s>your account</a> and manage your licenses."
msgstr ""

#. translators: %s: License manager link attributes.
#: inc/classes/class-uxthemes-api.php:154
msgid "The purchase code is already registered on another site. Please go to <a%s>your account</a> and manage your licenses."
msgstr ""

#: inc/classes/class-uxthemes-api.php:157
msgid "Your purchase code has been blocked. Please contact support to resolve the issue."
msgstr ""

#: inc/classes/class-uxthemes-api.php:160
msgid "The purchase code does not belong to a Flatsome sale."
msgstr ""

#: inc/classes/class-uxthemes-api.php:163
msgid "Flatsome was unable to get the latest version. The purchase code does not belong to a Flatsome sale."
msgstr ""

#: inc/classes/class-uxthemes-api.php:165
msgid "The requested resource no longer exists."
msgstr ""

#: inc/classes/class-uxthemes-api.php:167
msgid "No domain was sent with the request."
msgstr ""

#: inc/classes/class-uxthemes-api.php:169
msgid "Unable to parse the domain for your site."
msgstr ""

#: inc/classes/class-uxthemes-api.php:172
msgid "Your purchase code has been locked. Please contact support to resolve the issue."
msgstr ""

#: inc/classes/class-uxthemes-api.php:174
msgid "The requested resource has been locked."
msgstr ""

#: inc/classes/class-uxthemes-api.php:176
msgid "Sorry, the API is overloaded."
msgstr ""

#: inc/classes/class-uxthemes-api.php:178
msgid "Sorry, the API is unavailable at the moment."
msgstr ""

#: inc/extensions/flatsome-cookie-notice/flatsome-cookie-notice.php:50
msgctxt "cookie notice"
msgid "More info"
msgstr ""

#: inc/extensions/flatsome-cookie-notice/flatsome-cookie-notice.php:60
msgctxt "cookie notice"
msgid "Accept"
msgstr ""

#: inc/extensions/flatsome-live-search/flatsome-live-search.php:200
msgid "No matches found"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:87
msgid "Swatch size"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:90
msgid "X Small"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:91
msgid "Small"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:92
msgid "Medium"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:93
msgid "Large"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:94
msgid "X Large"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:96
msgid "Determines the size of the swatches."
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:100
msgid "Swatch shape"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:103
msgid "Square"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:104
msgid "Rounded"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:105
msgid "Circle"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:107
msgid "Determines the shape of the swatches."
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:111
#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:112
msgid "Use variation images?"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:113
msgid "Enable this if you want swatches for this attribute to be auto filled with variation images."
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches-admin.php:353
msgid "Value"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches.php:44
msgid "UX Color"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches.php:45
msgid "UX Image"
msgstr ""

#: inc/extensions/flatsome-swatches/includes/class-swatches.php:46
msgid "UX Label"
msgstr ""

#: inc/extensions/flatsome-wc-quick-view/flatsome-quick-view.php:15
msgid "Quick View"
msgstr "Snabbkoll"

#: inc/functions/function-custom-css.php:665
#: inc/woocommerce/structure-wc-global.php:332
#: inc/woocommerce/structure-wc-product-box.php:29
msgid "New"
msgstr "Ny"

#: inc/functions/function-custom-css.php:666
msgid "Hot"
msgstr "Het"

#: inc/functions/function-custom-css.php:667
msgid "Sale"
msgstr "Rea"

#: inc/functions/function-custom-css.php:668
msgid "Popular"
msgstr "Populär"

#. translators: %s: The label
#: inc/functions/function-register.php:55
msgid "Follow on %s"
msgstr ""

#: inc/functions/function-setup.php:114
#: inc/functions/function-setup.php:252
msgid "Main Menu"
msgstr ""

#: inc/functions/function-setup.php:115
msgid "Main Menu - Mobile"
msgstr ""

#: inc/functions/function-setup.php:116
msgid "Secondary Menu"
msgstr ""

#: inc/functions/function-setup.php:117
msgid "Footer Menu"
msgstr ""

#: inc/functions/function-setup.php:118
msgid "Top Bar Menu"
msgstr ""

#: inc/functions/function-setup.php:119
msgid "My Account Menu"
msgstr ""

#: inc/functions/function-setup.php:154
msgid "Sidebar"
msgstr ""

#: inc/functions/function-setup.php:164
msgid "Footer 1"
msgstr ""

#: inc/functions/function-setup.php:174
msgid "Footer 2"
msgstr ""

#: inc/functions/function-setup.php:253
#: inc/shortcodes/accordion.php:140
#: woocommerce/single-product/tabs/accordian.php:33
msgid "Toggle"
msgstr ""

#: inc/functions/function-site-health.php:11
msgid "Flatsome registration"
msgstr ""

#: inc/functions/function-site-health.php:28
msgid "Flatsome is registered"
msgstr ""

#: inc/functions/function-site-health.php:34
msgid "Register Flatsome to receive updates."
msgstr ""

#: inc/functions/function-site-health.php:40
msgid "Flatsome is not registered"
msgstr ""

#: inc/functions/function-site-health.php:44
msgid "Register now"
msgstr ""

#: inc/functions/function-site-health.php:48
msgid "Flatsome was unable to receive the latest update"
msgstr ""

#: inc/integrations/wc-yith-wishlist/yith-wishlist.php:73
#: inc/integrations/wc-yith-wishlist/yith-wishlist.php:96
#: template-parts/header/partials/element-wishlist-mobile.php:21
#: template-parts/header/partials/element-wishlist-mobile.php:22
#: template-parts/header/partials/element-wishlist.php:19
#: template-parts/header/partials/element-wishlist.php:24
#: template-parts/header/partials/element-wishlist.php:25
msgid "Wishlist"
msgstr ""

#. translators: %s: Comment count
#: inc/shortcodes/blog_posts.php:318
msgid "%s Comment"
msgid_plural "%s Comments"
msgstr[0] ""
msgstr[1] ""

#: inc/shortcodes/follow.php:127
#: inc/shortcodes/follow.php:128
msgid "Follow on Facebook"
msgstr ""

#: inc/shortcodes/follow.php:141
#: inc/shortcodes/follow.php:142
msgid "Follow on Instagram"
msgstr ""

#: inc/shortcodes/follow.php:155
#: inc/shortcodes/follow.php:156
msgid "Follow on TikTok"
msgstr ""

#: inc/shortcodes/follow.php:172
#: inc/shortcodes/follow.php:173
msgid "Follow on SnapChat"
msgstr ""

#: inc/shortcodes/follow.php:179
msgid "Point the SnapChat camera at this to add us to SnapChat."
msgstr ""

#: inc/shortcodes/follow.php:191
#: inc/shortcodes/follow.php:192
msgid "Follow on X"
msgstr ""

#: inc/shortcodes/follow.php:205
#: inc/shortcodes/follow.php:206
msgid "Follow on Twitter"
msgstr ""

#: inc/shortcodes/follow.php:219
#: inc/shortcodes/follow.php:220
msgid "Follow on Threads"
msgstr ""

#: inc/shortcodes/follow.php:233
#: inc/shortcodes/follow.php:234
msgid "Send us an email"
msgstr "Skicka ett mail till oss"

#: inc/shortcodes/follow.php:247
#: inc/shortcodes/follow.php:248
msgid "Call us"
msgstr ""

#: inc/shortcodes/follow.php:261
#: inc/shortcodes/follow.php:262
msgid "Follow on Pinterest"
msgstr ""

#: inc/shortcodes/follow.php:275
#: inc/shortcodes/follow.php:276
msgid "Subscribe to RSS"
msgstr ""

#: inc/shortcodes/follow.php:289
#: inc/shortcodes/follow.php:290
msgid "Follow on LinkedIn"
msgstr ""

#: inc/shortcodes/follow.php:303
#: inc/shortcodes/follow.php:304
msgid "Follow on YouTube"
msgstr ""

#: inc/shortcodes/follow.php:317
#: inc/shortcodes/follow.php:318
msgid "Flickr"
msgstr ""

#: inc/shortcodes/follow.php:331
#: inc/shortcodes/follow.php:332
msgid "Follow on 500px"
msgstr ""

#: inc/shortcodes/follow.php:345
#: inc/shortcodes/follow.php:346
msgid "Follow on VKontakte"
msgstr ""

#: inc/shortcodes/follow.php:359
#: inc/shortcodes/follow.php:360
msgid "Follow on Telegram"
msgstr ""

#: inc/shortcodes/follow.php:373
#: inc/shortcodes/follow.php:374
msgid "Follow on Twitch"
msgstr ""

#: inc/shortcodes/follow.php:387
#: inc/shortcodes/follow.php:388
msgid "Follow on Discord"
msgstr ""

#: inc/shortcodes/portfolio.php:146
#: woocommerce/product-searchform.php:39
msgid "All"
msgstr ""

#: inc/shortcodes/sections.php:150
msgid "Scroll for more"
msgstr ""

#: inc/shortcodes/share.php:96
#: inc/shortcodes/share.php:97
msgid "Share on WhatsApp"
msgstr ""

#: inc/shortcodes/share.php:111
#: inc/shortcodes/share.php:112
msgid "Share on Facebook"
msgstr "Dela på Facebook"

#: inc/shortcodes/share.php:125
#: inc/shortcodes/share.php:126
msgid "Share on X"
msgstr ""

#: inc/shortcodes/share.php:139
#: inc/shortcodes/share.php:140
msgid "Share on Twitter"
msgstr "Dela på Twitter"

#: inc/shortcodes/share.php:155
#: inc/shortcodes/share.php:156
msgid "Share on Threads"
msgstr ""

#. translators: %s: the share link.
#: inc/shortcodes/share.php:167
msgid "Check this out: %s"
msgstr ""

#: inc/shortcodes/share.php:171
#: inc/shortcodes/share.php:172
msgid "Email to a Friend"
msgstr "Maila en kompis"

#: inc/shortcodes/share.php:189
#: inc/shortcodes/share.php:190
msgid "Pin on Pinterest"
msgstr "Pinna på Pinterest"

#: inc/shortcodes/share.php:203
#: inc/shortcodes/share.php:204
msgid "Share on VKontakte"
msgstr ""

#: inc/shortcodes/share.php:217
#: inc/shortcodes/share.php:218
msgid "Share on LinkedIn"
msgstr ""

#: inc/shortcodes/share.php:231
#: inc/shortcodes/share.php:232
msgid "Share on Tumblr"
msgstr ""

#: inc/shortcodes/share.php:245
#: inc/shortcodes/share.php:246
msgid "Share on Telegram"
msgstr ""

#: inc/shortcodes/ux_instagram_feed.php:52
msgid "Please select an Instagram account"
msgstr ""

#: inc/shortcodes/ux_instagram_feed.php:222
msgid "Unable to communicate with Instagram."
msgstr ""

#: inc/shortcodes/ux_instagram_feed.php:224
msgid "An error occurred while retrieving media"
msgstr ""

#: inc/structure/structure-portfolio.php:50
msgctxt "breadcrumb"
msgid "Home"
msgstr ""

#: inc/structure/structure-posts.php:139
msgid "<span class=\"icon-angle-left\"></span> Older posts"
msgstr ""

#: inc/structure/structure-posts.php:144
msgid "Newer posts <span class=\"icon-angle-right\"></span>"
msgstr ""

#: inc/structure/structure-posts.php:168
msgid "Pingback:"
msgstr ""

#: inc/structure/structure-posts.php:184
msgid "%s <span class=\"says\">says:</span>"
msgstr ""

#: inc/structure/structure-posts.php:186
msgid "Your comment is awaiting moderation."
msgstr ""

#: inc/structure/structure-posts.php:195
msgctxt "1: date, 2: time"
msgid "%1$s at %2$s"
msgstr ""

#. translators: %1$s: post date, %2$s: post author
#: inc/structure/structure-posts.php:236
msgctxt "post date by post author"
msgid "<span class=\"posted-on\">Posted on %1$s</span> <span class=\"byline\">by %2$s</span>"
msgstr ""

#: inc/widgets/widget-blocks.php:17
msgid "Display a UX Block."
msgstr ""

#: inc/widgets/widget-blocks.php:21
msgid "Flatsome UX Blocks"
msgstr ""

#: inc/widgets/widget-blocks.php:85
#: inc/widgets/widget-recent-posts.php:129
#: inc/widgets/widget-upsell.php:120
msgid "Title"
msgstr ""

#: inc/widgets/widget-recent-posts.php:18
msgid "Display recent posts in Flatsome format."
msgstr ""

#: inc/widgets/widget-recent-posts.php:22
msgid "Flatsome Recent Posts"
msgstr ""

#: inc/widgets/widget-recent-posts.php:49
msgid "Recent Posts"
msgstr ""

#: inc/widgets/widget-recent-posts.php:86
#: template-parts/posts/partials/entry-footer-single.php:33
#: template-parts/posts/partials/entry-footer.php:33
msgid "<strong>1</strong> Comment"
msgstr ""

#: inc/widgets/widget-recent-posts.php:86
#: template-parts/posts/partials/entry-footer-single.php:33
#: template-parts/posts/partials/entry-footer.php:33
msgid "<strong>%</strong> Comments"
msgstr ""

#: inc/widgets/widget-recent-posts.php:132
msgid "Number of posts to show"
msgstr ""

#: inc/widgets/widget-recent-posts.php:136
msgid "Show thumbnail"
msgstr ""

#: inc/widgets/widget-recent-posts.php:139
msgid "Show date stamp on thumbnail"
msgstr ""

#: inc/widgets/widget-recent-posts.php:140
msgid "* If the \"Show Thumbnail\" option is disabled or no featured image is set, the date stamp will be displayed."
msgstr ""

#: inc/widgets/widget-upsell.php:20
msgid "Add upsell products to product pages."
msgstr ""

#: inc/widgets/widget-upsell.php:48
#: inc/widgets/widget-upsell.php:116
msgid "Complete the look"
msgstr "Fixa hela stilen"

#: inc/woocommerce/class-buy-now.php:100
msgid "Buy now"
msgstr ""

#: inc/woocommerce/class-mini-cart.php:168
msgctxt "mini cart add to cart button label"
msgid "Add"
msgstr ""

#. translators: %s: The threshold
#: inc/woocommerce/class-shipping.php:160
msgid "Add %s to cart and get free shipping!"
msgstr ""

#: inc/woocommerce/class-shipping.php:171
msgid "Your order qualifies for free shipping 🎉"
msgstr ""

#: inc/woocommerce/structure-wc-category-page.php:97
#: inc/woocommerce/structure-wc-product-page-fields.php:31
msgid "Top Content"
msgstr ""

#: inc/woocommerce/structure-wc-category-page.php:102
msgid "Enter a value for this field. Shortcodes are allowed. This will be displayed at top of the category."
msgstr ""

#: inc/woocommerce/structure-wc-category-page.php:119
#: inc/woocommerce/structure-wc-product-page-fields.php:38
msgid "Bottom Content"
msgstr ""

#: inc/woocommerce/structure-wc-category-page.php:124
msgid "Enter a value for this field. Shortcodes are allowed. This will be displayed at bottom of the category."
msgstr ""

#: inc/woocommerce/structure-wc-category-page.php:152
msgid "Choose a custom product block layout for this category."
msgstr ""

#: inc/woocommerce/structure-wc-checkout.php:166
msgid "I have read and agree"
msgstr ""

#: inc/woocommerce/structure-wc-global.php:94
msgid "Shop Sidebar"
msgstr ""

#: inc/woocommerce/structure-wc-global.php:103
msgid "Product Sidebar"
msgstr ""

#: inc/woocommerce/structure-wc-global.php:264
msgid "Posts found"
msgstr ""

#: inc/woocommerce/structure-wc-global.php:277
msgid "Pages found"
msgstr ""

#: inc/woocommerce/structure-wc-product-page-fields.php:17
msgid "Product layout"
msgstr ""

#: inc/woocommerce/structure-wc-product-page-fields.php:24
msgid "Choose a custom product block layout for this product."
msgstr ""

#: inc/woocommerce/structure-wc-product-page-fields.php:33
msgid "Enter content that will show after the header and before the product. Shortcodes are allowed"
msgstr ""

#: inc/woocommerce/structure-wc-product-page-fields.php:40
msgid "Enter content that will show after the product info. Shortcodes are allowed"
msgstr ""

#: inc/woocommerce/structure-wc-product-page-fields.php:46
msgid "Extra"
msgstr ""

#: inc/woocommerce/structure-wc-product-page-fields.php:52
msgid "Enable a custom bubble on this product."
msgstr ""

#: inc/woocommerce/structure-wc-product-page-fields.php:82
msgid "Custom Tab Content"
msgstr ""

#: inc/woocommerce/structure-wc-product-page-fields.php:84
msgid "Enter content for custom product tab here. Shortcodes are allowed"
msgstr ""

#: inc/woocommerce/structure-wc-product-page-fields.php:93
msgid "Product Video"
msgstr ""

#: inc/woocommerce/structure-wc-product-page-fields.php:95
msgid "Enter a Youtube or Vimeo Url of the product video here. We recommend uploading your video to Youtube."
msgstr ""

#: inc/woocommerce/structure-wc-product-page-fields.php:110
msgid "Select where you want to display product video."
msgstr ""

#: inc/woocommerce/structure-wc-product-page.php:73
#: inc/woocommerce/structure-wc-product-page.php:139
msgid "Video"
msgstr ""

#: inc/woocommerce/structure-wc-product-page.php:104
msgid "Zoom"
msgstr "Zooma"

#: inc/woocommerce/structure-wc-product-page.php:307
msgid "Select options"
msgstr ""

#. Template Name of the theme
msgid "Page - Full Width - Parallax Title"
msgstr ""

#. Template Name of the theme
msgid "Page - No Header / No Footer"
msgstr ""

#. Template Name of the theme
msgid "Page - Vertical Sub-Nav"
msgstr ""

#. Template Name of the theme
msgid "Page - Container - Center Title"
msgstr ""

#. Template Name of the theme
msgid "Page - Full Width"
msgstr ""

#. Template Name of the theme
msgid "WooCommerce - Cart"
msgstr ""

#. Template Name of the theme
msgid "WooCommerce - Checkout"
msgstr ""

#. Template Name of the theme
msgid "Portfolio"
msgstr ""

#. Template Name of the theme
msgid "Page - Full Width - Header on Scroll"
msgstr ""

#. Template Name of the theme
msgid "Page - Left Sidebar"
msgstr ""

#. Template Name of the theme
msgid "WooCommerce - My Account"
msgstr ""

#. Template Name of the theme
msgid "Page - Right Sidebar"
msgstr ""

#. Template Name of the theme
msgid "Page - Single Page Nav - Transparent Header - Light Text"
msgstr ""

#. Template Name of the theme
msgid "Page - Single Page Nav - Transparent Header"
msgstr ""

#. Template Name of the theme
msgid "Page - Single Page Nav"
msgstr ""

#. Template Name of the theme
msgid "Page - Full Width - Transparent Header - Light Text"
msgstr ""

#. Template Name of the theme
msgid "Page - Full Width - Transparent Header"
msgstr ""

#: searchform.php:18
#: woocommerce/product-searchform.php:65
msgid "Submit"
msgstr ""

#. translators: 1. Template
#: template-parts/admin/envato/directory-warning.php:13
msgid "An unusual theme directory name was detected: <em>%s</em>. The Flatsome parent theme should be installed in a directory named <em>flatsome</em> to ensure updates are handled correctly."
msgstr ""

#: template-parts/admin/envato/register-form.php:25
msgid "Your site is <strong>registered</strong>. Thank you! Enjoy Flatsome and one-click updates."
msgstr ""

#: template-parts/admin/envato/register-form.php:37
msgid "Your copy of Flatsome appears to be registered using an unsupported or outdated method. Please re-register with a valid purchase code."
msgstr ""

#: template-parts/admin/envato/register-form.php:52
msgid "Registered domain:"
msgstr ""

#: template-parts/admin/envato/register-form.php:110
msgid "Are you sure you want to unregister Flatsome?"
msgstr ""

#. translators: 1: Version.
#: template-parts/admin/envato/version-info-iframe.php:15
msgid "Version %s"
msgstr ""

#: template-parts/admin/envato/version-info-iframe.php:18
msgid "Read change log here"
msgstr ""

#: template-parts/footer/back-to-top.php:22
msgid "Go to top"
msgstr ""

#: template-parts/header/partials/element-contact-mobile.php:38
#: template-parts/header/partials/element-contact.php:33
msgid "Location"
msgstr ""

#: template-parts/header/partials/element-contact-mobile.php:47
#: template-parts/header/partials/element-contact.php:52
msgid "Contact"
msgstr ""

#: template-parts/header/partials/element-contact-mobile.php:81
#: template-parts/header/partials/element-contact.php:92
msgid "WhatsApp"
msgstr ""

#: template-parts/header/partials/element-menu-icon.php:13
#: template-parts/header/partials/element-menu-icon.php:17
#: template-parts/overlays/overlay-menu.php:45
msgid "Menu"
msgstr ""

#: template-parts/header/partials/element-nav-vertical.php:33
#: template-parts/overlays/overlay-menu.php:50
msgid "Categories"
msgstr ""

#: template-parts/portfolio/portfolio-summary-full.php:30
#: template-parts/portfolio/portfolio-summary.php:26
msgid "Tags"
msgstr ""

#: template-parts/posts/content-none.php:14
msgid "Nothing Found"
msgstr "Hittade inget"

#: template-parts/posts/content-none.php:20
msgid "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr ""

#: template-parts/posts/content-none.php:24
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "Ledsen, men vi hittade inte det du sökte efter. Prova igen med lite andra ord."

#: template-parts/posts/content-none.php:29
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."
msgstr "Det verkar som vi inte kan hitta det du letar efter. Kanske en sökning kan hjälpa?"

#. translators: used between list items, there is a space after the comma
#: template-parts/posts/content-single.php:31
#: template-parts/posts/content-single.php:34
#: template-parts/posts/partials/entry-footer-single.php:14
#: template-parts/posts/partials/entry-footer-single.php:22
#: template-parts/posts/partials/entry-footer.php:14
#: template-parts/posts/partials/entry-footer.php:22
#: template-parts/posts/partials/entry-title.php:12
#: template-parts/posts/partials/entry-title.php:17
msgid ", "
msgstr ""

#: template-parts/posts/content-single.php:39
msgid "This entry was posted in %1$s and tagged %2$s."
msgstr ""

#: template-parts/posts/content-single.php:41
msgid "This entry was posted in %1$s. Bookmark the <a href=\"%3$s\" title=\"Permalink to %4$s\" rel=\"bookmark\">permalink</a>."
msgstr ""

#: template-parts/posts/content.php:15
#: template-parts/posts/content.php:19
#: template-parts/posts/featured-posts.php:29
msgid "Continue reading <span class=\"meta-nav\">&rarr;</span>"
msgstr ""

#: template-parts/posts/loop/post-simple.php:28
msgid "%1$s Comment"
msgid_plural "%1$s Comments"
msgstr[0] ""
msgstr[1] ""

#: template-parts/posts/partials/archive-title.php:17
msgid "Category Archives: %s"
msgstr ""

#: template-parts/posts/partials/archive-title.php:20
msgid "Tag Archives: %s"
msgstr ""

#: template-parts/posts/partials/archive-title.php:23
msgid "Search Results for: %s"
msgstr "Resultat för: %s"

#: template-parts/posts/partials/archive-title.php:30
msgid "Author Archives: %s"
msgstr ""

#: template-parts/posts/partials/archive-title.php:38
msgid "Daily Archives: %s"
msgstr ""

#: template-parts/posts/partials/archive-title.php:41
msgid "Monthly Archives: %s"
msgstr ""

#: template-parts/posts/partials/archive-title.php:44
msgid "Yearly Archives: %s"
msgstr ""

#: template-parts/posts/partials/archive-title.php:47
msgid "Asides"
msgstr ""

#: template-parts/posts/partials/archive-title.php:50
msgid "Images"
msgstr ""

#: template-parts/posts/partials/archive-title.php:53
msgid "Videos"
msgstr ""

#: template-parts/posts/partials/archive-title.php:56
msgid "Quotes"
msgstr ""

#: template-parts/posts/partials/archive-title.php:59
msgid "Links"
msgstr ""

#: template-parts/posts/partials/entry-footer-single.php:17
#: template-parts/posts/partials/entry-footer.php:17
msgid "Posted in %1$s"
msgstr ""

#: template-parts/posts/partials/entry-footer-single.php:27
#: template-parts/posts/partials/entry-footer.php:27
msgid "Tagged %1$s"
msgstr ""

#: template-parts/posts/partials/entry-footer-single.php:33
#: template-parts/posts/partials/entry-footer.php:33
msgid "Leave a comment"
msgstr ""

#: woocommerce/checkout/header-small.php:36
#: woocommerce/checkout/header.php:38
msgid "Shopping Cart"
msgstr "Varukorg"

#: woocommerce/checkout/header-small.php:41
#: woocommerce/checkout/header.php:43
msgid "Checkout details"
msgstr "Kassan"

#: woocommerce/checkout/header-small.php:46
#: woocommerce/checkout/header.php:48
msgid "Order Complete"
msgstr "Ordern komplett"

#: woocommerce/checkout/social-login.php:17
#: woocommerce/myaccount/header.php:71
msgid "Login with <strong>Facebook</strong>"
msgstr ""

#: woocommerce/checkout/social-login.php:28
#: woocommerce/myaccount/header.php:81
msgid "Login with <strong>Google</strong>"
msgstr ""

#: woocommerce/myaccount/form-login-lightbox-left-panel.php:78
#: woocommerce/myaccount/form-login-lightbox-right-panel.php:120
msgid "Don't have an account? Register one!"
msgstr ""

#: woocommerce/myaccount/form-login-lightbox-left-panel.php:79
#: woocommerce/myaccount/form-login-lightbox-right-panel.php:121
msgid "Register an Account"
msgstr ""

#: inc/blocks/uxbuilder/block.json
msgctxt "block title"
msgid "UX Builder content"
msgstr ""

#: inc/blocks/uxbuilder/block.json
msgctxt "block description"
msgid "This block contains content created with UX Builder."
msgstr ""

#: theme.json
msgctxt "Color name"
msgid "Primary"
msgstr ""

#: theme.json
msgctxt "Color name"
msgid "Secondary"
msgstr ""

#: theme.json
msgctxt "Color name"
msgid "Success"
msgstr ""

#: theme.json
msgctxt "Color name"
msgid "Alert"
msgstr ""

