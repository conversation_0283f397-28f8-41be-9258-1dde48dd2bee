<?php return array('js/flatsome.js' => array('dependencies' => array('jquery'), 'version' => 'e1ad26bd5672989785e1'), 'js/woocommerce.js' => array('dependencies' => array('jquery'), 'version' => 'dd6035ce106022a74757'), 'js/flatsome-relay.js' => array('dependencies' => array(), 'version' => 'bf43260455bcbdff6904'), 'js/flatsome-pjax.js' => array('dependencies' => array(), 'version' => 'e78097aaf6d04232da17'), 'css/flatsome.js' => array('dependencies' => array(), 'version' => '482f146852c238753c52'), 'css/flatsome-rtl.js' => array('dependencies' => array(), 'version' => 'add56b4d4f32e1447996'), 'css/flatsome-shop.js' => array('dependencies' => array(), 'version' => 'a5c31bc8d61dca622ae5'), 'css/flatsome-shop-rtl.js' => array('dependencies' => array(), 'version' => '9db78fb75ccf7ee3bfdc'), 'css/effects.js' => array('dependencies' => array(), 'version' => '992fe8d3aea9cfe4c157'), 'css/editor.js' => array('dependencies' => array(), 'version' => '279a298a2f9b65e9acb3'), 'js/builder/vendors/vendors.js' => array('dependencies' => array('jquery'), 'version' => '1c257985f1367aef23c0'), 'js/builder/core/editor.js' => array('dependencies' => array('jquery', 'wp-autop'), 'version' => '3a561788b07d4c4d4fdd'), 'js/builder/core/content.js' => array('dependencies' => array('wp-autop'), 'version' => '91dc07f478c12b7f7388'), 'js/builder/custom/editor.js' => array('dependencies' => array(), 'version' => 'e8ceb1291b9743ce879c'), 'js/builder/custom/content.js' => array('dependencies' => array('jquery'), 'version' => '4ffb5ea24ef66eec6ea1'), 'css/builder/core/editor.js' => array('dependencies' => array(), 'version' => 'bf85255a3574e392e338'), 'css/builder/core/content.js' => array('dependencies' => array(), 'version' => '8f5ef5279b8c50b6f6fa'), 'css/builder/custom/builder.js' => array('dependencies' => array(), 'version' => 'abf4fc9ead096e35431b'), 'js/admin/admin-menu.js' => array('dependencies' => array(), 'version' => '6f53ec37c0460cd0a383'), 'js/admin/customizer-admin.js' => array('dependencies' => array(), 'version' => '17d0a5babc7bf411c57f'), 'js/admin/customizer-frontend.js' => array('dependencies' => array(), 'version' => '220982990c73ccc605d5'), 'css/admin/admin-customizer.js' => array('dependencies' => array(), 'version' => 'fc67ad4b91aa23a2ce6c'), 'css/admin/admin-frontend.js' => array('dependencies' => array(), 'version' => '247f0a0861009048297b'), 'css/admin/admin-header-builder.js' => array('dependencies' => array(), 'version' => 'f22b0ed7d441e68a61fb'), 'css/admin/admin-menu.js' => array('dependencies' => array(), 'version' => '50a4a280b8fe2110a9d3'), 'js/extensions/flatsome-swatches-admin.js' => array('dependencies' => array(), 'version' => '7945246d2e2db0e015c0'), 'js/extensions/flatsome-swatches-frontend.js' => array('dependencies' => array(), 'version' => '5ec72a966f11f253f725'), 'js/extensions/flatsome-variation-images-admin.js' => array('dependencies' => array(), 'version' => '025c9df5f16f56265067'), 'js/extensions/flatsome-variation-images-frontend.js' => array('dependencies' => array(), 'version' => '5c193207db9374b2893c'), 'js/extensions/flatsome-ajax-add-to-cart-frontend.js' => array('dependencies' => array(), 'version' => '70391503da33f8644570'), 'css/extensions/flatsome-swatches-admin.js' => array('dependencies' => array(), 'version' => '70ec9856a4b20527fc00'), 'css/extensions/flatsome-swatches-frontend.js' => array('dependencies' => array(), 'version' => 'cb0e99055b11f014a1bb'), 'css/extensions/flatsome-swatches-frontend-rtl.js' => array('dependencies' => array(), 'version' => '394350b887c88e8b3f20'), 'css/extensions/flatsome-variation-images-admin.js' => array('dependencies' => array(), 'version' => '27d113c4153582cc3d14'));
